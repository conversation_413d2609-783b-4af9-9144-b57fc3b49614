from hijri_converter import convert
from datetime import date
from docx import Document
from docx.shared import Pt, Inches
from docx.oxml import OxmlElement
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from google.cloud import vision
from typing import List
import re, json, os, base64, platform, cv2, numpy as np, hashlib, time
from ai_functions import *
from PIL import Image
import math

# Global cache for Google Vision API results
_vision_cache = {}
_cache_file = "vision_cache.json"

# Global cache for OCR results during card processing session
_session_ocr_cache = {}

def clear_session_ocr_cache():
    """Clear the session OCR cache"""
    global _session_ocr_cache
    _session_ocr_cache = {}

def crop_personal_photo_face_detection(image_path, output_path=None, padding_x=0, padding_y=0, debugging=False):
    """
    Detect and crop personal photo using Haar cascade face detection.

    Args:
        image_path (str): Path to the ID card image
        output_path (str): Path to save the cropped photo (optional)
        padding_x (float): Horizontal padding as percentage of face width (default: 0.35 = 35%)
        padding_y (float): Vertical padding as percentage of face height (default: 0.55 = 55%)
        debugging (bool): Enable debug prints

    Returns:
        dict: Result with success status, path, method used, and details
    """
    try:
        # Read the image
        image = cv2.imread(image_path)
        if image is None:
            return {"success": False, "error": "Could not read image", "method": "face_detection"}

        height, width = image.shape[:2]
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Try to load face cascade
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

        if len(faces) == 0:
            return {"success": False, "error": "No faces detected", "method": "face_detection"}

        # Use the largest detected face
        largest_face = max(faces, key=lambda f: f[2] * f[3])
        x, y, w, h = largest_face

        # Add padding around the face to include full head
        padding_x_px = int(w * padding_x)  # Horizontal padding in pixels
        padding_y_px = int(h * padding_y)  # Vertical padding in pixels (more for hair/forehead)

        # Adjust coordinates with padding
        x = max(0, x - padding_x_px)
        y = max(0, y - padding_y_px)
        w = min(width - x, w + 2 * padding_x_px)
        h = min(height - y, h + 2 * padding_y_px)

        # Crop the face region
        cropped_photo = image[y:y+h, x:x+w]

        # Save the cropped photo
        if output_path is None:
            # Create face_photos folder if it doesn't exist
            face_photos_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'face_photos')
            os.makedirs(face_photos_folder, exist_ok=True)

            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_path = os.path.join(face_photos_folder, f"{base_name}_personal_photo.jpg")

        cv2.imwrite(output_path, cropped_photo)

        return {
            "success": True,
            "path": output_path,
            "method": "face_detection",
            "bbox": (x, y, w, h),
            "faces_detected": len(faces),
            "details": f"Face at ({x}, {y}) with size {w}x{h}, padding: {padding_x_px}x{padding_y_px} ({padding_x*100:.0f}%x{padding_y*100:.0f}%)"
        }

    except Exception as e:
        return {"success": False, "error": str(e), "method": "face_detection"}

def crop_personal_photo_from_card(image_path, output_path=None, padding_x=0.2, padding_y=0.2, debugging=False):
    """
    Detect and crop the personal photo from an ID card using face detection.

    Based on comprehensive testing, face detection provides the best results with:
    - 100% success rate on test images
    - Automatic face detection and sizing
    - Configurable padding (default: 35% horizontal, 55% vertical) to include full head
    - Adapts to different face positions and sizes

    Args:
        image_path (str): Path to the ID card image
        output_path (str): Path to save the cropped photo (optional)
        padding_x (float): Horizontal padding as percentage of face width (default: 0.35 = 35%)
        padding_y (float): Vertical padding as percentage of face height (default: 0.55 = 55%)
        debugging (bool): Enable debug prints

    Returns:
        str or None: Path to the cropped photo if successful, None otherwise
    """
    result = crop_personal_photo_face_detection(image_path, output_path, padding_x, padding_y, debugging)

    if result["success"]:
        if debugging:
            print(f"Personal photo saved to: {result['path']}")
        return result["path"]
    else:
        if debugging:
            print(f"✗ Failed to crop personal photo: {result['error']}")
        return None

def get_image_hash(image_path):
    """Generate a hash for an image file to use as cache key"""
    try:
        with open(image_path, 'rb') as f:
            # Read file in chunks to handle large files efficiently
            hash_md5 = hashlib.md5()
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return None

def load_vision_cache():
    """Load the vision cache from disk"""
    global _vision_cache
    try:
        if os.path.exists(_cache_file):
            with open(_cache_file, 'r', encoding='utf-8') as f:
                _vision_cache = json.load(f)
    except Exception:
        _vision_cache = {}

def save_vision_cache():
    """Save the vision cache to disk"""
    try:
        with open(_cache_file, 'w', encoding='utf-8') as f:
            json.dump(_vision_cache, f, ensure_ascii=False, indent=2)
    except Exception:
        pass

def get_cached_vision_result(image_path, operation_type="text_detection"):
    """
    Get cached Google Vision API result for an image

    Args:
        image_path: Path to the image file
        operation_type: Type of operation ("text_detection" or "document_text_detection")

    Returns:
        Cached result or None if not found
    """
    image_hash = get_image_hash(image_path)
    if not image_hash:
        return None

    cache_key = f"{image_hash}_{operation_type}"
    return _vision_cache.get(cache_key)

def cache_vision_result(image_path, result, operation_type="text_detection"):
    """
    Cache a Google Vision API result

    Args:
        image_path: Path to the image file
        result: The API result to cache
        operation_type: Type of operation ("text_detection" or "document_text_detection")
    """
    image_hash = get_image_hash(image_path)
    if not image_hash:
        return

    cache_key = f"{image_hash}_{operation_type}"
    _vision_cache[cache_key] = {
        'result': result,
        'timestamp': time.time(),
        'image_path': os.path.basename(image_path)  # For debugging
    }

    # Save cache to disk periodically (every 10 entries)
    if len(_vision_cache) % 10 == 0:
        save_vision_cache()

# Load cache on module import
load_vision_cache()

# Save cache when module is unloaded
import atexit
atexit.register(save_vision_cache)

def detect_text_rotation_opencv(image_path, debugging=False):
    """
    Detect text rotation using OpenCV without Google Vision API.
    Uses edge detection and line detection to determine text orientation.

    Args:
        image_path: Path to the image file
        debugging: Enable debug prints

    Returns:
        tuple: (rotation_angle, rotated_image_path) or (0, None) if no rotation needed
    """
    try:
        # Read the image
        image = cv2.imread(image_path)
        if image is None:
            if debugging:
                print(f"Could not read image: {image_path}")
            return 0, None

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Apply edge detection
        edges = cv2.Canny(blurred, 50, 150, apertureSize=3)

        # Use HoughLines to detect lines
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

        if lines is None:
            if debugging:
                print("No lines detected for rotation analysis")
            return 0, None

        # Calculate angles of detected lines
        angles = []
        for line in lines:
            rho, theta = line[0]
            angle = math.degrees(theta) - 90  # Convert to rotation angle
            # Normalize angle to [-90, 90] range
            if angle > 90:
                angle -= 180
            elif angle < -90:
                angle += 180
            angles.append(angle)

        if not angles:
            return 0, None

        # Find the most common angle (mode)
        # Group angles into bins of 5 degrees
        angle_bins = {}
        for angle in angles:
            bin_key = round(angle / 5) * 5
            angle_bins[bin_key] = angle_bins.get(bin_key, 0) + 1

        # Find the bin with the most votes
        most_common_angle = max(angle_bins, key=angle_bins.get)

        if debugging:
            print(f"Detected rotation angle: {most_common_angle}°")

        # Only rotate if angle is significant (> 5 degrees)
        if abs(most_common_angle) > 5:
            # Rotate the image
            height, width = image.shape[:2]
            center = (width // 2, height // 2)

            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -most_common_angle, 1.0)

            # Calculate new image dimensions
            cos_angle = abs(rotation_matrix[0, 0])
            sin_angle = abs(rotation_matrix[0, 1])
            new_width = int((height * sin_angle) + (width * cos_angle))
            new_height = int((height * cos_angle) + (width * sin_angle))

            # Adjust rotation matrix for new center
            rotation_matrix[0, 2] += (new_width / 2) - center[0]
            rotation_matrix[1, 2] += (new_height / 2) - center[1]

            # Apply rotation
            rotated_image = cv2.warpAffine(image, rotation_matrix, (new_width, new_height),
                                         flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

            # Save rotated image
            cv2.imwrite(image_path, rotated_image)

            if debugging:
                print(f"Image rotated by {-most_common_angle}° and saved")

            return most_common_angle, image_path
        else:
            if debugging:
                print("No significant rotation detected")
            return 0, None

    except Exception as e:
        if debugging:
            print(f"Error in rotation detection: {e}")
        return 0, None

def crop_cards(image_path, output_folder="Ready Cards", cards_to_detect=2, min_detection_area=4, debugging=True):
    """
    Main function to detect and crop cards from an image using advanced color separation method

    Args:
        image_path: Path to the input image
        output_folder: Folder to save cropped cards (default: "Ready Cards")
        cards_to_detect: Number of cards to detect (default: 1)
        min_detection_area: Minimum area percentage for card detection (default: 4)
        debugging: Enable debug prints (default: True)

    Returns:
        List of paths to cropped card images
    """

    def color_based_enhancement(image):
        """Enhance based on color differences"""
        # Convert to LAB color space for better color separation
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        l = clahe.apply(l)

        # Enhance A and B channels
        a = cv2.convertScaleAbs(a, alpha=1.2, beta=10)
        b = cv2.convertScaleAbs(b, alpha=1.2, beta=10)

        # Merge and convert back
        enhanced_lab = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

        return enhanced

    def detect_cards_color_separation(image, min_area_percentage=4.0, debugging=False):
        """Detect cards using color separation method"""
        if debugging:
            print(f"Starting card detection with min area: {min_area_percentage}%")

        # Calculate minimum area in pixels from percentage
        total_area = image.shape[0] * image.shape[1]
        min_area = (min_area_percentage / 100.0) * total_area

        if debugging:
            print(f"Image size: {image.shape[1]}x{image.shape[0]}, Total area: {total_area}, Min area: {min_area}")

        # Apply standard color enhancement
        processed_image = color_based_enhancement(image)

        # Convert to LAB color space
        lab = cv2.cvtColor(processed_image, cv2.COLOR_BGR2LAB)

        # Use L channel from LAB for edge detection
        working_channel = lab[:,:,0]

        # Adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        equalized = clahe.apply(working_channel)

        # Edge detection
        edges = cv2.Canny(equalized, 30, 100)

        # Morphological operations to close gaps
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        final_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # Find contours
        contours, _ = cv2.findContours(final_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < 0.3:  # Skip very elongated shapes
                continue

            detections.append({
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio
            })

        # Sort by area (smallest to largest as requested)
        detections.sort(key=lambda x: x['area'])

        if debugging:
            print(f"Found {len(detections)} potential card detections")
            for i, det in enumerate(detections):
                area_pct = (det['area'] / total_area) * 100
                print(f"  Detection {i+1}: {det['bbox']}, area: {area_pct:.1f}%")

        return detections



    # Main crop_cards logic starts here
    if debugging:
        print(f"Processing image: {image_path}")
        print(f"Looking for {cards_to_detect} card(s)")

    # Create output directory
    os.makedirs(output_folder, exist_ok=True)

    # Load image
    image = cv2.imread(image_path)
    if image is None:
        if debugging:
            print(f"Error: Could not load image {image_path}")
        return []

    # Always check for rotation first, regardless of card detection
    if debugging:
        print("Checking image rotation...")

    # NOTE: Disabled OpenCV rotation detection to prevent double rotation
    # The Google Vision API rotation detection in detect_rotation_and_keyword()
    # is more accurate for text-based documents and will handle rotation correction

    # Create a temporary copy of the original image for rotation check
    # temp_rotation_check = "temp_rotation_check.jpg"
    # cv2.imwrite(temp_rotation_check, image)

    # Use OpenCV-based rotation detection instead of Google Vision API
    # rotation_angle, rotated_path = detect_text_rotation_opencv(temp_rotation_check, debugging=debugging)

    # If the image was rotated, reload it
    # if rotation_angle != 0 and rotated_path:
    #     image = cv2.imread(temp_rotation_check)
    #     if debugging:
    #         print(f"Image was rotated by {rotation_angle}°, reloaded for detection")

    # Clean up temporary file
    # if os.path.exists(temp_rotation_check):
    #     os.remove(temp_rotation_check)

    # Detect cards using color separation
    detections = detect_cards_color_separation(image, min_area_percentage=min_detection_area, debugging=debugging)

    if not detections:
        if debugging:
            print("No cards detected, saving processed image")
        # Save the processed (potentially rotated) image to output directory
        filename = os.path.basename(image_path)
        output_path = os.path.join(output_folder, filename)
        cv2.imwrite(output_path, image)
        return [output_path]

    result_paths = []
    cards_found = 0

    # Process detections from smallest to largest
    for i, detection in enumerate(detections):
        if cards_found >= cards_to_detect:
            break

        x, y, w, h = detection['bbox']

        if debugging:
            area_pct = (detection['area'] / (image.shape[0] * image.shape[1])) * 100
            print(f"Processing detection {i+1}: area {area_pct:.1f}%")

        # Crop the detected region
        cropped = image[y:y+h, x:x+w]

        # Save cropped image temporarily for OCR check
        temp_filename = f"temp_crop_{i}.jpg"
        cv2.imwrite(temp_filename, cropped)

        # Check if this crop contains "Kingdom" using OCR and cache the result
        ocr_result, detected_text = detect_rotation_and_keyword(temp_filename, debugging=debugging)

        if ocr_result:
            # Cache the OCR text for later use in type determination
            image_hash = get_image_hash(temp_filename)
            if image_hash:
                _session_ocr_cache[image_hash] = detected_text
            # This is a valid card, save it to output directory
            filename = os.path.splitext(os.path.basename(image_path))[0]
            output_filename = f"{filename}_card_{cards_found+1}.jpg"
            output_path = os.path.join(output_folder, output_filename)

            # Read the potentially rotated image and save to final location
            final_cropped = cv2.imread(temp_filename)
            cv2.imwrite(output_path, final_cropped)
            result_paths.append(output_path)
            cards_found += 1

            if debugging:
                print(f"Valid card found and saved: {output_path}")
        else:
            if debugging:
                print(f"Detection {i+1} rejected: no 'Kingdom' keyword found")

        # Clean up temporary file
        if os.path.exists(temp_filename):
            os.remove(temp_filename)

    if cards_found == 0:
        if debugging:
            print("No valid cards found, saving original image")
        # Save original image to output directory
        filename = os.path.basename(image_path)
        output_path = os.path.join(output_folder, filename)
        cv2.imwrite(output_path, image)
        result_paths.append(output_path)

    if debugging:
        print(f"Processing complete. Found {cards_found} valid card(s)")

    return result_paths

def fetch_ai_prompt(file_type, ocr_lines="", required_info=""): 

    additional_guidelines = ""

    if file_type == "id":

        additional_guidelines = """

        •	Add (H) at the end of Hijri dates and add (G) at the end of the Gregorian Dates.

"""
    
    elif file_type == "one-page" or file_type == "one-page-new" or file_type == "two-pages" or file_type == "two-pages-new":

        additional_guidelines = f"""

        Family Card Additional Guidelines
        •	Please use the Family Head Name (which is the first name in the OCR lines, usually the father but can be the mother in rare cases) to generate a shortened name consisting of First Name + Last Name
        •	Please note that the first family member is probably the spouse (Either wife or husband) and their info followed by children’s names and info, you should choose the spouse relevance as either (Wife) or (Husband)
        •   Sometimes, the spouse might be dead or divorced and thus removed from the card, if you do not detect (Wife) or (Husband) in the ocr result, then return "spouse_relavance": "no spouse" and keep other spouse info empty ""
        •	(children’s relevance can be "Son" or “Daughter” or "Son - Independent" or "Daughter - Married")
        •	if the child first name only exist, then only use his first name, if his full name is present, then translate the full name.
                
"""

    elif file_type == "tm":
        prompt = r"""
        Please analyze the trademark by identifying the Arabic text in the trademark, the non-Arabic text and its meaning, and visually describing the logo.

        For the Trademark language, you have 3 choices:
        1.	If the logo contains ONLY English or non-arabic characters or words, the Tm language is: لاتيني فقط
        2.	If the logo contains ONLY Arabic characters or words, the Tm language is: عربي فقط
        3.	If the logo contains both English AND Arabic characters or words, the Tm language is: عربي ولاتيني

        For the Arabic name (اسم العلامة العربي): Here you should insert all the Arabic characters and words in the logo, for example, if the logo has the word “خالد” and the letter “خ” in it, the Arabic name should be “خ خالد”. If the logo doesn’t contain any Arabic words or letters, leave this field empty. 

        For the Latin name (اسم العلامة اللاتيني): Here you should insert all the non-Arabic characters and words in the logo, for example, if the logo has the word “Khaled” and the letter “K” in it, the Latin name should be: “K Khaled”. If the logo doesn’t contain any English words or letters, leave this field empty. PLEASE BE EXTRA CAREFUL AND MENTION ALL NON-ARABIC CHARACTERS IN THIS FIELD

        Logo Translation (المعنى باللغة العربية): Here you should provide the meaning for the non-Arabic words. Please learn from these examples and mimic their style and format for similar logo names:

        1.	Bamboo: كلمة لاتينية وتعني: خيزران
        B: حرف لاتيني، وهو اختصار لكلمة (bamboo)

        2.	Almostakbal: كلمة عربية الأصل (المستقبل) مكتوبة بأحرف لاتينية، وتعني باللغة العربية: المستقبل، وهو الزمن القادم، وهو ضد الماضي
        Real Estate Development: جملة لاتينية وتعني: للتطوير العقاري

        فيكون معنى الاسم ككل: المستقبل للتطوير العقاري

        3.	FLEXBANDS: كلمة لاتينية مركبة من كلمتين وهما Flex وتعني (مرن) وكلمة Bands وتعني (مجموعات أو عصبات)، فيكون معنى الكلمة ككل: المجموعات المرنة

        4.	Carb Less: عبارة لاتينية وتعني: بدون كربوهيدرات
        CL: اختصار للعبارة اللاتينية (Carb Less)

        Sometimes, the logo contains Arabic words written in English characters, in this case, the logo should be translated as per these examples (please make sure to explain the meaning of the Arabic words like a dictionary):

        1.	DKHOON AL-HARAMAIN: ليس لها معنى باللغة اللاتينية أو الإنجليزية، وهي جملة عربية الأصل (دخون الحرمين) مكتوبة بأحرف لاتينية، أما معناها باللغة العربية فهو: دخون الحرمين، حيث كلمة دخون هي جمع كلمة دُخَان، وهو الغاز المتطاير في الهواء، والحرمين هما الحرم المكي والحرم المدني
        2.	Memar Almughmais: جملة عربية الأصل (معمار المغامس) مكتوبة بأحرف لاتينية، حيث كلمة (معمار) تعني الشخص أو الجهة التي تقوم بالبناء والتعمير، وكلمة (المغامس) هي عبارة عن اسم فقط وليس لها معنى
        3.	aban: كلمة عربية الأصل (ابان) مكتوبة بأحرف لاتينية، وتعني: أظهر وبيّن

        In other cases, the logo may contain names that do not have a meaning neither in English, Arabic, and any other language, in this case you should translate them like this: 

        1.	NUVAN: كلمة مبتكرة، ليس لها معنى، وهي اسم فقط
        2.	ENALA: كلمة مبتكرة، ليس لها معنى، وهي اسم فقط


        For the pronunciation in Arabic, (طريقة النطق), you should simply determine the pronunciation for ALL non-Arabic words and characters and write it in Arabic like this (بامبو). Examples:

        1.	(لاف سبا)
        2.	(هوت مييت\nسموكي تيست)
        3.	(بي آر\nبيان روعة)

        For the description (الوصف), here you visually describe the logo, and you should start describing the most-above element or right-most element. You should also add the colors for the text and shapes. The description must not contain English letters, commas, full stops, parentheses, or quotations. It must only contain Arabic words and characters. Here is how you should refer to words in the description:
        "كلمة بامبو مكتوبة بأحرف لاتينية باللون الأزرق الغامق" or “كلمة بامبو مكتوبة بأحرف عربية باللون الأزرق”
        And here are some examples of descriptions for different logos:

        1.	عبارة همم واعدة مكتوبة بأحرف عربية باللون الأسود وبخط مميز ويوجد أسفلها عبارة تأهيل القيادات الواعدة مكتوبة بأحرف عربية باللون الأسود ويوجد أسفلها عبارة بروميسينج ليدرشيب ديفيلوبمنت مكتوبة بأحرف لاتينية باللون الأسود وبخط مميز ومتصل ويوجد على الجانب الأيسر رسم دائري يظهر رجلا بالزي السعودي جالسا على جبل ويحمل عصا وينظر الى الأفق الذي يحتوي على سماء صافية وثلاث طيور ودائرة والرسم ككل باللون الأزرق الفاتح والغامق والبني الفاتح والغامق والأخضر الغامق والأبيض وخلفية الشعار باللون البيج
        2.	رسم لصحن دائري باللون البني الغامق وبداخله حلقة يعلوها شوكة وسكين متقاطعتين باللون البيج ويوجد على يساره رسم لدلة قهوة عربية باللون البني الغامق وإلى يسارها قليلاً يوجد صحن مائدة مع غطاء باللون البني الغامق ويوجد أعلاه رسم لفنجان قهوة ينبعث منه بخار موضوع على صحن باللون البني الغامق ويوجد في الأسفل عبارة ركن الخليلي مكتوبة بأحرف عربية باللون البني الغامق وبطريقة مميزة والخلفية باللون البيج 
        3.	كلمة ويزي باللون الأزرق ويوجد على يسارها مربع باللون الأزرق ويوجد بداخله الحرف اللاتيني زي باللون الأبيض حيث تم رسم القسم العلوي من الحرف على شكل سهم متجه الى اليسار باللون الأبيض وتم رسم القسم السفلي من الحرف على شكل سهم يتجه الى اليمين باللون الأبيض
        4.	كلمة سي إل مكتوبة بأحرف لاتينية باللون الأخضر وبطريقة مميزة حيث تم رسم الحرف الأول في الأعلى باللون الأزرق وتم رسم الحرف الثاني أسفله باللون الأحمر وبشكل مائل ويوجد في نهاية الحرف الثاني رسم لدائرة بداخلها سنبلة يعلوها خط مستقيم يقطعها باللون الأصفر كما يوجد في منتصف الحرف الثاني عبارة كارب ليس مكتوبة بأحرف لاتينية باللون الأخضر والخلفية باللون البيج الفاتحPlease include a main name property, which contains the main trademark English name
        Note: Do not mention the background color if it's white! Only say it when it is not white
        
        Lastly, your response must be a valid JSON variable, otherwise our system will break, exactly like this:

        {
            "main_name": "Bamboo",
            "tm_language": "عربي ولاتيني",
            "ar_name": "بامبو",
            "en_name": "B Bamboo",
            "meaning": "Bamboo: كلمة لاتينية وتعني: خيزران\nB: Bambooحرف لاتيني، وهو اختصار لكلمة ”,
            "pronunciation": "بي\nبامبو",
            "description": "كلمة بامبو مكتوبة بأحرف عربية باللون الأزرق ويوجد اسفلها كلمة بامبو مكتوبة بأحرف لاتينية باللون الأزرق الغامق ويوجد على الجهة اليسرى رسم يشبه الكأس باللون الأخضر وفي داخله يوجد شكل حلزوني متصل بساق نبتة عليه ورقتين باللون الأزرق التركوازي"
        }

        According to the above, analyze the attached logo


        """
    
    elif file_type == "tm-meaning":
        prompt = r"""
        
        Please analyze the trademark by identifying the trademark language, the Arabic text in the trademark, the non-Arabic text and its meaning, and how to pronounce the non-Arabic text with Arabic words.

        For the Trademark language, you have 3 choices:
        1.	If the logo contains ONLY English or non-arabic characters or words, the Tm language is: لاتيني فقط
        2.	If the logo contains ONLY Arabic characters or words, the Tm language is: عربي فقط
        3.	If the logo contains both English AND Arabic characters or words, the Tm language is: عربي ولاتيني

        For the Arabic name (اسم العلامة العربي): Here you should insert all the Arabic characters and words in the logo, for example, if the logo has the word “خالد” and the letter “خ” in it, the Arabic name should be “خ خالد”. If the logo doesn’t contain any Arabic words or letters, leave this field empty. 

        For the Latin name (اسم العلامة اللاتيني): Here you should insert all the non-Arabic characters and words in the logo, for example, if the logo has the word “Khaled” and the letter “K” in it, the Latin name should be: “K Khaled”. If the logo doesn’t contain any English words or letters, leave this field empty. PLEASE BE EXTRA CAREFUL AND MENTION ALL NON-ARABIC CHARACTERS IN THIS FIELD

        Logo Translation (المعنى باللغة العربية): Here you should provide the meaning for the non-Arabic words. Please learn from these examples and mimic their style and format for similar logo names:

        1.	bamboo: كلمة لاتينية وتعني: خيزران
        2.	Real Estate Development: جملة لاتينية وتعني: للتطوير العقاري

        Sometimes, the logo contains Arabic words written in English characters, in this case, the logo should be translated as per these examples (please learn from them):

        1.	DKHOON AL-HARAMAIN: ليس لها معنى باللغة اللاتينية أو الإنجليزية، وهي جملة عربية الأصل (دخون الحرمين) مكتوبة بأحرف لاتينية، أما معناها باللغة العربية فهو: دخون الحرمين، حيث كلمة دخون هي جمع دُخَان
        2.	Memar Almughmais: جملة عربية الأصل (معمار المغامس) مكتوبة بأحرف لاتينية، حيث كلمة (معمار) تعني الشخص أو الجهة التي تقوم بالبناء والتعمير، وكلمة (المغامس) هي عبارة عن اسم فقط وليس لها معنى
        3.	BEIT OMDTNA: ليس لها معنى باللغة اللاتينية أو الإنجليزية، وهي كلمة عربية الأصل (بيت عمدتنا) مكتوبة بأحرف لاتينية، وتعني بالعربية: منزل العمدة الخاص بنا

        In other cases, the logo may contain names that do not have a meaning neither in English, Arabic, and any other language, in this case you should translate them like this: 

        1.	NUVAN: كلمة مبتكرة، ليس لها معنى، وهي اسم فقط
        2.	ENALA: كلمة مبتكرة، ليس لها معنى، وهي اسم فقط

        For the pronunciation in Arabic, (طريقة النطق), you should simply determine the pronunciation for ALL non-Arabic words and characters and write it in Arabic like this (بامبو). Example: if a logo contains (Q Qadib) and (قاضب), then the correct pronunciation will be (كيو\nقاضب) AND NOT (كيو\nكادب). Other Examples:
        1.	(لاف سبا)
        2.	(هوت مييت\nسموكي تيست)
        3.	(بي آر\nبيان روعة)
        Please include a main name property, which contains the main trademark English name
        Lastly, your response must be a valid JSON variable, otherwise our system will break, exactly like this:

        {
            "main_name": "Bamboo",
            "tm_language": "عربي ولاتيني",
            "ar_name": "بامبو",
            "en_name": "B Bamboo",
            "meaning": "Bamboo: كلمة لاتينية وتعني: خيزران\nB: Bambooحرف لاتيني، وهو اختصار لكلمة ”,
            "pronunciation": "بي\nبامبو"
        }

        According to the above, analyze the attached logo

        """
    
    elif file_type == "tm-description":
        prompt = r"""
        Please describe the attached trademark according to these guidelines:
        In the trademark description (الوصف), you should visually describe the logo, and you should start describing the most-above element or right-most element. You should also add the colors for the text and shapes. The description must not contain English letters, commas, full stops, parentheses, or quotations. It must only contain Arabic words and characters. Here is how you should refer to words in the description:
        "كلمة بامبو مكتوبة بأحرف لاتينية باللون الأزرق الغامق" or “كلمة بامبو مكتوبة بأحرف عربية باللون الأزرق”
        And here are some examples of descriptions for different logos:

        1.	بطاقة مربعة باللون الأزرق ويوجد بداخلها كلمة ايفكو مكتوبة بأحرف عربية باللون الأبيض ويوجد أسفلها كلمة إيفكو مكتوبة بأحرف لاتينية باللون الأبيض 
        2.	بطاقة مربعة تحتوي على رسومات مختلفة إبداعية تشبه البقع باللون الوردي والوردي الفاتح والأبيض ويوجد في المنتصف كلمة لافر مكتوبة بأحرف لاتينية باللون الرمادي وبخط كبير ويوجد اسفلها كلمة تشوكليت مكتوبة بأحرف لاتينية باللون الرمادي وبخط صغير
        3.	كلمة ويزي باللون الأزرق ويوجد على يسارها مربع باللون الأزرق ويوجد بداخله الحرف اللاتيني زي باللون الأبيض حيث تم رسم القسم العلوي من الحرف على شكل سهم متجه الى اليسار باللون الأبيض وتم رسم القسم السفلي من الحرف على شكل سهم يتجه الى اليمين باللون الأبيض
        4.	شكل رباعي يشبه السهم المتجه للأسفل بحدود باللون البرتقالي ويوجد بداخله الحرف اللاتيني إتش مكتوب بطريقة مميزة باللون الأزرق والبرتقالي ويوجد على يساره خط عمودي باللون الأزرق ويوجد على يساره جملة حلم السعودية مكتوبة بأحرف عربية باللون الأزرق ويوجد أسفلها جملة للخدمات اللوجستية مكتوبة بأحرف لاتينية باللون البرتقالي ويوجد اسفلها جملة سعودي حلم لوجستكس سيرفسس مكتوبة بأحرف لاتينية باللون الأزرق
        Please include a main name property, which contains the main trademark English name
        Lastly, your response must be a valid JSON variable, otherwise our system will break, exactly like this:
        {
        "description": "بطاقة مربعة باللون الأزرق الفاتح ويوجد بداخلها الحرف اللاتيني بي باللون الأزرق ويوجد اسفله كلمة بامبو مكتوبة بأحرف عربية باللون الأزرق ويوجد اسفلها كلمة بامبو مكتوبة بأحرف لاتينية باللون الأزرق الغامق وعلى اليسار يوجد رسم يشبه الكأس باللون الأخضر وفي داخله يوجد شكل حلزوني متصل بساق نبتة عليه ورقتين باللون الأزرق التركوازي"
        }

        According to the above, describe the attached logo
        """
    
    if "tm" not in file_type:

        prompt = f"""

        The attached text is a response from the OCR software, which consists of a set of lines, please help us organize the data, translate it into English, and give us the result in JSON format.

        Guidelines:
        •	Translated Hijri dates must stay in Hijri, both Hijri and Gregorian dates should look like this: dd/mm/yyyy.
        •	If some info is not provided in the OCR lines, leave it as empty strings "", but not Null
        •   All people names must be UPPERCASE
        •	All data you provide must be translated to English, DO NOT PROVIDE ANY ARABIC WORDS OR NUMBERS! Especially names of cities and villages, you should search for their correct spelling, this is Extremely Important! NO ARABIC AT ALL!
        •	Please add this character (^) to long IDs between every 3 digits, such as (229^357^5768), or last 4 digits of the ID if that what remains

        {additional_guidelines}

        here is the OCR lines:

        {ocr_lines}

        This is how your response should look like, never add explanations or any extra characters, otherwise our code will break:

        {required_info}

"""

    return prompt

def rotate_image(image, angle):
    """Rotate image by given angle using OpenCV"""
    if angle == 0:
        return image

    # Get image dimensions
    h, w = image.shape[:2]
    center = (w // 2, h // 2)

    # Calculate rotation matrix
    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Calculate new dimensions after rotation
    cos_angle = abs(rotation_matrix[0, 0])
    sin_angle = abs(rotation_matrix[0, 1])
    new_w = int((h * sin_angle) + (w * cos_angle))
    new_h = int((h * cos_angle) + (w * sin_angle))

    # Adjust rotation matrix for new center
    rotation_matrix[0, 2] += (new_w / 2) - center[0]
    rotation_matrix[1, 2] += (new_h / 2) - center[1]

    # Perform rotation
    rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h),
                            flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT,
                            borderValue=(255, 255, 255))

    return rotated

def detect_rotation_and_keyword(image_path, keyword=None, debugging=False, rotation_only=False):
    """Detect rotation and keyword using Google Vision OCR with caching

    Returns:
        tuple: (success_bool, detected_text) or bool for backward compatibility
    """
    import math
    from google.cloud import vision

    if debugging:
        print(f"Checking OCR and rotation for: {image_path}")

    try:
        # Check cache first
        cached_result = get_cached_vision_result(image_path, "document_text_detection")

        if cached_result:
            if debugging:
                print(f"Using cached result for {os.path.basename(image_path)}")
            response_data = cached_result['result']

            # Reconstruct response object from cached data
            detected_text = response_data.get('detected_text', '')
            pages_data = response_data.get('pages_data', [])
        else:
            if debugging:
                print(f"Making Google Vision API call for {os.path.basename(image_path)}")

            # Initialize Google Vision client
            client = vision.ImageAnnotatorClient()

            # Read image
            with open(image_path, 'rb') as image_file:
                content = image_file.read()

            # Create Vision API image object
            image = vision.Image(content=content)

            # Perform document text detection (includes both text and orientation info)
            response = client.document_text_detection(image=image)

            if response.error.message:
                if debugging:
                    print(f"Google Vision API error: {response.error.message}")
                return (False, "")

            # Extract detected text
            detected_text = ""
            if response.full_text_annotation:
                detected_text = response.full_text_annotation.text

            # Extract pages data for rotation calculation
            pages_data = []
            if response.full_text_annotation and response.full_text_annotation.pages:
                for page in response.full_text_annotation.pages:
                    page_data = []
                    if page.blocks:
                        for block in page.blocks:
                            for paragraph in block.paragraphs:
                                for word in paragraph.words:
                                    vertices = word.bounding_box.vertices
                                    if len(vertices) >= 2:
                                        dx = vertices[1].x - vertices[0].x
                                        dy = vertices[1].y - vertices[0].y
                                        angle = math.degrees(math.atan2(dy, dx))
                                        page_data.append(angle)
                    pages_data.append(page_data)

            # Cache the result
            cache_data = {
                'detected_text': detected_text,
                'pages_data': pages_data
            }
            cache_vision_result(image_path, cache_data, "document_text_detection")

        if debugging:
            print(f"Detected text: {detected_text[:100]}...")

        # If rotation_only is True, skip keyword checking
        if not rotation_only:
            # Check for "Kingdom" or "الصلة" keywords
            kingdom_found = ("kingdom" in detected_text.lower() or
                            "المملكة" in detected_text or
                            "الصلة" in detected_text)

            # Check for additional keyword if provided
            keyword_found = True
            if keyword:
                keyword_found = keyword.lower() in detected_text.lower()

            if debugging:
                print(f"Kingdom/الصلة found: {kingdom_found}, Keyword '{keyword}' found: {keyword_found}")

            if not (kingdom_found and keyword_found):
                return (False, detected_text)

        # Calculate rotation from text orientation using cached or fresh data
        rotation_angle = 0
        if pages_data and pages_data[0]:  # Use cached pages data
            angles = pages_data[0]
            if angles:
                avg_rotation = sum(angles) / len(angles)

                if debugging:
                    print(f"Average rotation from {len(angles)} text elements: {avg_rotation:.1f}°")

                # Normalize to -90, 0, 90 degrees with more conservative thresholds
                if avg_rotation > 60:  # More conservative threshold
                    rotation_angle = 90
                elif avg_rotation < -60:  # More conservative threshold
                    rotation_angle = -90
                elif 30 < avg_rotation <= 60:  # Check for 45° rotation
                    rotation_angle = 45
                elif -60 <= avg_rotation < -30:  # Check for -45° rotation
                    rotation_angle = -45
                else:
                    rotation_angle = 0

        if debugging:
            print(f"Detected rotation: {rotation_angle}°")

        # If rotation is needed, rotate and save the image
        if rotation_angle != 0:
            image_cv = cv2.imread(image_path)

            if debugging:
                print(f"Text orientation detected: {rotation_angle}°")

            # Use OpenCV's built-in rotation for 90° increments (more reliable)
            if rotation_angle == 90:
                # Text is rotated 90° clockwise, so rotate image 90° counter-clockwise
                rotated_image = cv2.rotate(image_cv, cv2.ROTATE_90_COUNTERCLOCKWISE)
                if debugging:
                    print("Applied 90° counter-clockwise rotation")
            elif rotation_angle == -90:
                # Text is rotated 90° counter-clockwise, so rotate image 90° clockwise
                rotated_image = cv2.rotate(image_cv, cv2.ROTATE_90_CLOCKWISE)
                if debugging:
                    print("Applied 90° clockwise rotation")
            else:
                # For other angles, use the custom rotation function
                correction_angle = -rotation_angle
                rotated_image = rotate_image(image_cv, correction_angle)
                if debugging:
                    print(f"Applied {correction_angle}° rotation")
            cv2.imwrite(image_path, rotated_image)
            if debugging:
                print(f"Image corrected and saved")

        # If rotation_only is True, always return True (we just wanted to fix rotation)
        if rotation_only:
            return (True, detected_text)

        return (True, detected_text)

    except Exception as e:
        if debugging:
            print(f"Error in OCR detection: {e}")
        return (False, "")

def flip_image(filename, direction):
    """
    Rotate an image based on the direction provided and copy to clipboard.

    Args:
        filename (str): The filename of the image to rotate
        direction (str): The rotation direction. Can be "-90", "90", or "0"

    Returns:
        str: The filename of the rotated image, or the original filename if no rotation was needed
    """
    # Convert direction string to angle
    if direction == "-90":
        angle = -90
    elif direction == "90":
        angle = 90
    else:
        return filename  # No rotation needed

    # Load image with OpenCV for rotation
    image_cv = cv2.imread(filename)
    if image_cv is None:
        print(f"Error: Could not load image {filename}")
        return filename

    # Use OpenCV's built-in rotation for 90° increments (more reliable)
    if angle == 90:
        rotated_image = cv2.rotate(image_cv, cv2.ROTATE_90_CLOCKWISE)
    elif angle == -90:
        rotated_image = cv2.rotate(image_cv, cv2.ROTATE_90_COUNTERCLOCKWISE)
    else:
        # For other angles, use the custom rotation function
        rotated_image = rotate_image(image_cv, angle)

    # Save the rotated image with a new filename
    base, ext = os.path.splitext(filename)
    new_filename = f"{base}_flipped{ext}"
    cv2.imwrite(new_filename, rotated_image)

    # Convert to PIL Image for clipboard operations
    with Image.open(new_filename) as img:
        # Put the rotated image in the clipboard based on the operating system
        if platform.system() == 'Windows':
            import win32clipboard
            from io import BytesIO

            output = BytesIO()
            img.convert('RGB').save(output, 'BMP')
            data = output.getvalue()[14:]  # Remove BMP header
            output.close()

            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
            win32clipboard.CloseClipboard()

        elif platform.system() == 'Darwin':  # macOS
            import subprocess

            img.save('temp_clipboard.png')
            subprocess.run(['osascript', '-e',
                          'set the clipboard to (read (POSIX file "./temp_clipboard.png") as JPEG picture)'])
            os.remove('temp_clipboard.png')

        elif platform.system() == 'Linux':
            import subprocess

            img.save('temp_clipboard.png')
            subprocess.run(['xclip', '-selection', 'clipboard', '-t', 'image/png', '-i', 'temp_clipboard.png'])
            os.remove('temp_clipboard.png')

        print("Rotated image copied to clipboard")

    return new_filename

def determine_card_type(image_path, debugging=False, cached_ocr_text=None):
    """
    Determine the type of document based on OCR text analysis.

    Args:
        image_path (str): Path to the image file
        debugging (bool): Enable debug prints
        cached_ocr_text (str): Pre-computed OCR text to avoid API call

    Returns:
        str: The determined card type
    """
    try:
        # Use cached OCR text if available, otherwise perform OCR
        if cached_ocr_text:
            detected_text = cached_ocr_text
            if debugging:
                print(f"Using cached OCR text for {os.path.basename(image_path)}")
        else:
            # Check session cache first
            image_hash = get_image_hash(image_path)
            if image_hash and image_hash in _session_ocr_cache:
                detected_text = _session_ocr_cache[image_hash]
                if debugging:
                    print(f"Using session cached OCR text for {os.path.basename(image_path)}")
            else:
                # Check persistent cache before making API call
                cached_result = get_cached_vision_result(image_path, "text_detection")
                if cached_result:
                    result_data = cached_result['result']
                    ocr_lines = result_data['lines']
                    detected_text = " ".join(ocr_lines)
                    if debugging:
                        print(f"Using persistent cached OCR text for {os.path.basename(image_path)}")
                else:
                    # Perform OCR on the image
                    ocr_lines, _ = perform_ocr(image_path)
                    detected_text = " ".join(ocr_lines)

                # Cache the result for this session
                if image_hash:
                    _session_ocr_cache[image_hash] = detected_text

        # Normalize text for consistent comparison - convert to lowercase
        detected_text_lower = detected_text.lower()

        if debugging:
            print(f"OCR text for card type detection: {detected_text[:200]}...")

        # ID Cards - Arabic text (case doesn't matter for Arabic, but keeping consistent)
        if "الهوية الوطنية" in detected_text:
            if "جهة" in detected_text:
                card_type = "old_id"
            elif "التحقق" in detected_text:
                card_type = "new_id"
            else:
                card_type = "normal_id"

        # Iqama Cards - Mixed Arabic and English text
        # Handle both "resident identity" and "identity resident" (text can be read in different orders)
        elif ("resident identity" in detected_text_lower or "identity resident" in detected_text_lower):  # English text - use lowercase
            if "المهنة" in detected_text:  # Arabic text
                card_type = "iqama_normal"
            elif "الأسرة" in detected_text:  # Arabic text
                card_type = "iqama_normal_dependent"
            else:
                card_type = "iqama_normal"

        elif "هوية مقيم" in detected_text:  # Arabic text
            if "التحقق" in detected_text:  # Arabic text
                if "المهنة" in detected_text:  # Arabic text
                    card_type = "iqama_absher"
                elif "الأسرة" in detected_text:  # Arabic text
                    card_type = "iqama_absher_dependent"
                else:
                    card_type = "iqama_absher"
            else:
                card_type = "iqama_normal"

        # Family Cards - Arabic text
        elif "سجل الأسرة" in detected_text:
            if "التحقق" in detected_text:
                if "الصلة" in detected_text:
                    card_type = "new_family_back"
                else:
                    card_type = "new_family_front"
            elif "المدنية" in detected_text:
                card_type = "family_front"
            elif "الصلة" in detected_text:
                card_type = "new_family_back"
            else:
                card_type = "family_front"

        elif "الصلة" in detected_text and ("افراد" in detected_text or "أفراد" in detected_text) and "سجل الأسرة" not in detected_text:
            card_type = "family_back"

        # Birth Certificate - Arabic text
        elif "شهادة ميلاد" in detected_text:
            card_type = "birth_certificate"

        # Passport - English text (case insensitive)
        elif "passport" in detected_text_lower:
            card_type = "passport"

        else:
            card_type = "unknown"

        if debugging:
            print(f"Determined card type: {card_type}")

        return card_type

    except Exception as e:
        if debugging:
            print(f"Error determining card type: {e}")
        return "unknown"

def rename_image_by_type(image_path, card_type, index=1, debugging=False):
    """
    Rename an image file based on its determined card type.

    Args:
        image_path (str): Path to the image file
        card_type (str): The determined card type
        index (int): Index number for multiple files of same type
        debugging (bool): Enable debug prints

    Returns:
        str: Path to the renamed file
    """
    try:
        directory = os.path.dirname(image_path)
        extension = os.path.splitext(image_path)[1]

        # Create new filename based on card type
        type_names = {
            "old_id": "Old_ID",
            "new_id": "New_ID",
            "normal_id": "Normal_ID",
            "iqama_normal": "Iqama_Normal",
            "iqama_normal_dependent": "Iqama_Normal_Dependent",
            "iqama_absher": "Iqama_Absher",
            "iqama_absher_dependent": "Iqama_Absher_Dependent",
            "family_front": "Family_Card_Front",
            "family_back": "Family_Card_Back",
            "new_family_front": "New_Family_Card_Front",
            "new_family_back": "New_Family_Card_Back",
            "birth_certificate": "Birth_Certificate",
            "passport": "Passport",
            "unknown": "Unknown_Document"
        }

        type_name = type_names.get(card_type, "Unknown_Document")
        new_filename = f"{type_name}_{index:02d}{extension}"
        new_path = os.path.join(directory, new_filename)

        # Handle duplicate names
        counter = 1
        while os.path.exists(new_path):
            new_filename = f"{type_name}_{index:02d}_{counter}{extension}"
            new_path = os.path.join(directory, new_filename)
            counter += 1

        # Rename the file
        os.rename(image_path, new_path)

        if debugging:
            print(f"Renamed {os.path.basename(image_path)} -> {os.path.basename(new_path)}")

        return new_path

    except Exception as e:
        if debugging:
            print(f"Error renaming file: {e}")
        return image_path

def process_pdf_file(temp_file_path, date_folder_path, debugging=False):
    """
    Process a PDF file by converting it to images and compressing them.

    Args:
        temp_file_path (str): Path to the temporary PDF file
        date_folder_path (str): Path to the date folder for output
        debugging (bool): Enable debug prints

    Returns:
        list: List of compressed image paths
    """
    processed_images = []

    if debugging:
        print(f"Converting PDF: {os.path.basename(temp_file_path)}")

    converted_images = convert_pdf_to_image(temp_file_path, date_folder_path, debugging=debugging)

    if converted_images:
        if debugging:
            print(f"✓ PDF converted successfully, processing {len(converted_images)} images")

        for img_path in converted_images:
            compressed_path = compress_img(img_path, debugging=debugging)
            processed_images.append(compressed_path)
    else:
        if debugging:
            print(f"✗ Failed to convert PDF: {os.path.basename(temp_file_path)}")

    return processed_images

def process_image_file(temp_file_path, date_folder_path, original_filename, debugging=False):
    """
    Process an image file by moving it to the destination and compressing it.

    Args:
        temp_file_path (str): Path to the temporary image file
        date_folder_path (str): Path to the date folder for output
        original_filename (str): Original filename from upload
        debugging (bool): Enable debug prints

    Returns:
        str or None: Path to the compressed image, or None if failed
    """
    import uuid
    import time

    # Create safe filename for final destination
    extension = os.path.splitext(original_filename)[1]

    try:
        # Try to use original filename first
        safe_final_name = original_filename
        final_image_path = os.path.join(date_folder_path, safe_final_name)
    except Exception:
        # If original name causes issues, use safe name
        safe_final_name = f"image_{int(time.time())}_{uuid.uuid4().hex[:8]}{extension}"
        final_image_path = os.path.join(date_folder_path, safe_final_name)

    # If file already exists in destination, create unique name
    counter = 1
    while os.path.exists(final_image_path):
        name_part = os.path.splitext(safe_final_name)[0]
        ext_part = os.path.splitext(safe_final_name)[1]
        new_filename = f"{name_part}_{counter}{ext_part}"
        final_image_path = os.path.join(date_folder_path, new_filename)
        counter += 1

    try:
        # Verify temp file exists before moving
        if not os.path.exists(temp_file_path):
            if debugging:
                print(f"ERROR: Temp file does not exist: {temp_file_path}")
            return None

        # Move file to final location
        os.rename(temp_file_path, final_image_path)

        # Verify the image file is valid before compression
        try:
            with Image.open(final_image_path) as test_img:
                test_img.verify()
            if debugging:
                print(f"✓ Image verified: {os.path.basename(final_image_path)}")
        except Exception as verify_error:
            if debugging:
                print(f"✗ Image verification failed: {str(verify_error)}")
            if os.path.exists(final_image_path):
                os.remove(final_image_path)
            return None

        # Compress the image
        compressed_path = compress_img(final_image_path, debugging=debugging)
        return compressed_path

    except Exception as move_error:
        if debugging:
            print(f"✗ Error moving image file: {str(move_error)}")
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        return None

def crop_and_classify_images(processed_images, cropped_cards_path, debugging=False):
    """
    Crop cards from images and classify them by type.
    Also extracts names and dates of birth from each document.

    Args:
        processed_images (list): List of processed image paths
        cropped_cards_path (str): Path to the cropped cards folder
        debugging (bool): Enable debug prints

    Returns:
        tuple: (list of classified and renamed cropped card paths, dict of extracted data)
               extracted_data format: {
                   'names': [list of all extracted names],
                   'document_data': [list of dicts with per-document data]
               }
    """
    # Don't clear session cache - preserve it across the entire auto mode operation
    # clear_session_ocr_cache()

    all_cropped_cards = []
    card_type_counters = {}

    # Initialize data extraction containers
    extracted_data = {
        'names': [],
        'document_data': []
    }

    for img_path in processed_images:
        try:
            # Extract names from the original image (before cropping)
            # This ensures we capture data from documents that might not contain valid cards
            try:
                # Get OCR text for the original image
                _, original_lines = perform_ocr(img_path)

                # Use original lines for name extraction (preserves correct order)
                original_text = "\n".join(original_lines)

                # Check if this is a passport document (supporting document)
                is_passport = "passport" in original_text.lower()

                if is_passport:
                    if debugging:
                        print(f"📘 Detected passport document: {os.path.basename(img_path)} - extracting names only")

                    # For passport documents, only extract names (no further processing needed)
                    document_names = extract_names_from_ocr(original_text)

                    # Store document-specific data
                    document_info = {
                        'image_path': img_path,
                        'names': document_names,
                        'is_passport': True
                    }
                    extracted_data['document_data'].append(document_info)

                    # Add to global lists
                    extracted_data['names'].extend(document_names)

                    if debugging and document_names:
                        print(f"✓ Extracted from passport {os.path.basename(img_path)}: {len(document_names)} names")

                    # Skip card cropping and classification for passport documents
                    continue

                # For non-passport documents, proceed with normal processing
                # Extract names only
                document_names = extract_names_from_ocr(original_text)

                # Store document-specific data
                document_info = {
                    'image_path': img_path,
                    'names': document_names,
                    'is_passport': False
                }
                extracted_data['document_data'].append(document_info)

                # Add to global lists
                extracted_data['names'].extend(document_names)

                if debugging and document_names:
                    print(f"✓ Extracted from {os.path.basename(img_path)}: {len(document_names)} names")

            except Exception as extract_error:
                if debugging:
                    print(f"⚠ Could not extract data from {os.path.basename(img_path)}: {extract_error}")

            # Crop cards from the image (only for non-passport documents)
            cropped_results = crop_cards(img_path, output_folder=cropped_cards_path, debugging=debugging)

            if cropped_results:
                if debugging:
                    print(f"✓ Cropped {len(cropped_results)} cards from {os.path.basename(img_path)}")

                # Process each cropped card
                for cropped_path in cropped_results:
                    # Check if we have cached OCR text for this image
                    image_hash = get_image_hash(cropped_path)
                    cached_ocr_text = _session_ocr_cache.get(image_hash) if image_hash else None

                    # Determine card type using cached OCR text if available
                    card_type = determine_card_type(cropped_path, debugging=debugging, cached_ocr_text=cached_ocr_text)

                    # Update counter for this card type
                    if card_type not in card_type_counters:
                        card_type_counters[card_type] = 0
                    card_type_counters[card_type] += 1

                    # Rename the file based on card type
                    renamed_path = rename_image_by_type(
                        cropped_path,
                        card_type,
                        card_type_counters[card_type],
                        debugging=debugging
                    )

                    all_cropped_cards.append(renamed_path)
            else:
                if debugging:
                    print(f"⚠ No cards found in {os.path.basename(img_path)}")

        except Exception as crop_error:
            if debugging:
                print(f"✗ Error cropping cards from {img_path}: {str(crop_error)}")

    if debugging:
        print(f"✓ Total extracted: {len(extracted_data['names'])} names")

    return all_cropped_cards, extracted_data

def extract_names_from_ocr(ocr_text):
    """
    Extract names from OCR text by looking for patterns with commas.
    Names typically follow the pattern: LASTNAME, FIRSTNAME MIDDLENAME

    Args:
        ocr_text (str): OCR text to search for names

    Returns:
        list: List of extracted names
    """
    import re

    names = []
    lines = ocr_text.split('\n')

    for line in lines:
        line = line.strip()
        # Look for patterns with comma that could be names
        # Names usually have format: LASTNAME, FIRSTNAME MIDDLENAME
        if ',' in line:
            # Split by comma and check if it looks like a name
            parts = line.split(',')
            if len(parts) >= 2:
                lastname = parts[0].strip()
                firstname_part = parts[1].strip()

                # Check if this looks like a name (contains letters, not just numbers/symbols)
                # Filter out common non-name patterns but be more lenient
                if (re.search(r'[A-Za-z\u0600-\u06FF]', lastname) and
                    re.search(r'[A-Za-z\u0600-\u06FF]', firstname_part) and
                    len(lastname) > 1 and len(firstname_part) > 1 and
                    not any(word in line.lower() for word in ['date', 'birth', 'person', 'id', 'card', 'number'])):

                    # Clean up the name parts (remove extra spaces and common prefixes)
                    # Handle cases like "Name/ ALSHURAIM, ALI MOHAMMED A"
                    lastname_clean = re.sub(r'^(another\s+person:\s*|name[/:\s]*)', '', lastname, flags=re.IGNORECASE).strip()
                    firstname_clean = firstname_part.strip()

                    if lastname_clean and firstname_clean:
                        # Reconstruct the full name
                        full_name = f"{lastname_clean}, {firstname_clean}"
                        names.append(full_name)

    return names

def extract_dates_from_ocr(ocr_text):
    """
    Extract dates of birth from OCR text.
    Prioritizes dates near "birth" keywords, then looks for dd/mm/yyyy format and dd MMM yyyy format.

    Args:
        ocr_text (str): OCR text to search for dates

    Returns:
        list: List of extracted dates in dd/mm/yyyy format
    """
    import re
    from datetime import datetime

    dates = []
    lines = ocr_text.split('\n')

    # Convert Arabic numerals to English
    text = arabic_to_english_numbers(ocr_text)

    # First, look for dates near "birth" keywords
    birth_dates = []
    for i, line in enumerate(lines):
        line_lower = line.lower()
        if 'birth' in line_lower or 'born' in line_lower:
            # Check current line and next few lines for dates
            search_lines = lines[i:i+3]  # Current line + next 2 lines
            for search_line in search_lines:
                search_text = arabic_to_english_numbers(search_line)

                # Pattern 1: dd/mm/yyyy format
                date_matches = re.findall(r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b', search_text)
                for match in date_matches:
                    day, month, year = match
                    try:
                        datetime(int(year), int(month), int(day))
                        formatted_date = f"{day.zfill(2)}/{month.zfill(2)}/{year}"
                        if formatted_date not in birth_dates:
                            birth_dates.append(formatted_date)
                    except ValueError:
                        continue

                # Pattern 2: dd MMM yyyy format
                month_names = {
                    'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
                    'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12',
                    'january': '01', 'february': '02', 'march': '03', 'april': '04', 'may': '05', 'june': '06',
                    'july': '07', 'august': '08', 'september': '09', 'october': '10', 'november': '11', 'december': '12'
                }

                date_matches2 = re.findall(r'\b(\d{1,2})\s+([a-zA-Z]{3,9})\s+(\d{4})\b', search_text, re.IGNORECASE)
                for match in date_matches2:
                    day, month_str, year = match
                    month_num = month_names.get(month_str.lower())
                    if month_num:
                        try:
                            datetime(int(year), int(month_num), int(day))
                            formatted_date = f"{day.zfill(2)}/{month_num}/{year}"
                            if formatted_date not in birth_dates:
                                birth_dates.append(formatted_date)
                        except ValueError:
                            continue

                # Pattern 3: yyyy MMM dd format (reverse order)
                date_matches3 = re.findall(r'\b(\d{4})\s+([a-zA-Z]{3,9})\s+(\d{1,2})\b', search_text, re.IGNORECASE)
                for match in date_matches3:
                    year, month_str, day = match
                    month_num = month_names.get(month_str.lower())
                    if month_num:
                        try:
                            datetime(int(year), int(month_num), int(day))
                            formatted_date = f"{day.zfill(2)}/{month_num}/{year}"
                            if formatted_date not in birth_dates:
                                birth_dates.append(formatted_date)
                        except ValueError:
                            continue

    # If we found birth dates, prioritize them
    if birth_dates:
        dates.extend(birth_dates)

    # Also find other dates in the document (but add them after birth dates)
    # Pattern 1: dd/mm/yyyy format
    date_pattern1 = r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b'
    matches1 = re.findall(date_pattern1, text)

    for match in matches1:
        day, month, year = match
        try:
            # Validate the date
            datetime(int(year), int(month), int(day))
            formatted_date = f"{day.zfill(2)}/{month.zfill(2)}/{year}"
            if formatted_date not in dates:  # Avoid duplicates
                dates.append(formatted_date)
        except ValueError:
            # Invalid date, skip
            continue

    # Pattern 2: dd MMM yyyy format (e.g., "3 Sep 1983")
    month_names = {
        'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
        'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12',
        'january': '01', 'february': '02', 'march': '03', 'april': '04', 'may': '05', 'june': '06',
        'july': '07', 'august': '08', 'september': '09', 'october': '10', 'november': '11', 'december': '12'
    }

    date_pattern2 = r'\b(\d{1,2})\s+([a-zA-Z]{3,9})\s+(\d{4})\b'
    matches2 = re.findall(date_pattern2, text, re.IGNORECASE)

    for match in matches2:
        day, month_str, year = match
        month_num = month_names.get(month_str.lower())
        if month_num:
            try:
                # Validate the date
                datetime(int(year), int(month_num), int(day))
                formatted_date = f"{day.zfill(2)}/{month_num}/{year}"
                if formatted_date not in dates:  # Avoid duplicates
                    dates.append(formatted_date)
            except ValueError:
                # Invalid date, skip
                continue

    # Pattern 3: yyyy MMM dd format (reverse order, e.g., "1983 Mar 14")
    date_pattern3 = r'\b(\d{4})\s+([a-zA-Z]{3,9})\s+(\d{1,2})\b'
    matches3 = re.findall(date_pattern3, text, re.IGNORECASE)

    for match in matches3:
        year, month_str, day = match
        month_num = month_names.get(month_str.lower())
        if month_num:
            try:
                # Validate the date
                datetime(int(year), int(month_num), int(day))
                formatted_date = f"{day.zfill(2)}/{month_num}/{year}"
                if formatted_date not in dates:  # Avoid duplicates
                    dates.append(formatted_date)
            except ValueError:
                # Invalid date, skip
                continue

    return dates

def arabic_to_english_numbers(text):
    """Convert Arabic/Persian numerals to English numerals."""
    arabic_numbers = '٠١٢٣٤٥٦٧٨٩'
    english_numbers = '0123456789'
    trans = str.maketrans(arabic_numbers, english_numbers)
    return text.translate(trans)

def parse_date_string(date_str: str) -> tuple:
    """
    Parse date string in various formats and return year, month, day.
    Supports formats:
    - dd/mm/yyyy, dd-mm-yyyy
    - yyyy/mm/dd, yyyy-mm-dd
    - Arabic numerals
    Filters out any alphabetic characters (A-Z, a-z)
    """
    # Remove any alphabetic characters
    date_str = re.sub(r'[a-zA-Z]', '', date_str)
    
    # Convert Arabic numerals to English if present
    date_str = arabic_to_english_numbers(date_str)

    # Remove any spaces and extra characters
    date_str = date_str.strip()

    # Replace various separators with a standard one
    date_str = re.sub(r'[/.-]', '/', date_str)

    # Remove any duplicate separators that might have been created
    date_str = re.sub(r'/+', '/', date_str)
    
    # Remove leading/trailing separators
    date_str = date_str.strip('/')

    # Split the date string
    parts = date_str.split('/')

    if len(parts) != 3:
        raise ValueError("Invalid date format. Use dd/mm/yyyy or yyyy/mm/dd")

    try:
        # Convert all parts to integers
        parts = [int(p) for p in parts]
    except ValueError:
        raise ValueError("Date parts must be valid numbers")

    # Check if the first part is a year (assuming year > 100)
    if parts[0] > 100:
        # Format is yyyy/mm/dd
        year, month, day = parts
    else:
        # Format is dd/mm/yyyy
        day, month, year = parts

    return year, month, day

def convert_date(date_str: str, conversion_type: str = 'h2g') -> tuple:
    """
    Convert dates between Hijri and Gregorian calendars.

    Args:
        date_str (str): Date string in format dd/mm/yyyy, yyyy/mm/dd,
                       or with Arabic numerals
        conversion_type (str): Type of conversion - 'h2g' for Hijri to Gregorian,
                             'g2h' for Gregorian to Hijri

    Returns:
        tuple: (date_object, formatted_string)

    Raises:
        ValueError: If conversion_type is invalid or date is invalid
    """
    try:
        # Parse the input date string
        year, month, day = parse_date_string(date_str)

        # Perform the conversion
        if conversion_type.lower() == 'h2g':
            # Convert Hijri to Gregorian
            converted_date = convert.Hijri(year, month, day).to_gregorian()
        elif conversion_type.lower() == 'g2h':
            # Convert Gregorian to Hijri
            converted_date = convert.Gregorian(year, month, day).to_hijri()
        else:
            raise ValueError("conversion_type must be either 'h2g' or 'g2h'")

        # Create date object
        date_obj = date(converted_date.year, converted_date.month, converted_date.day)

        # Create formatted string
        date_str = f"{converted_date.day:02d}/{converted_date.month:02d}/{converted_date.year}"

        return date_obj, date_str

    except (ValueError, OverflowError, TypeError, AttributeError) as e:
        print(f"Date conversion error: {str(e)} for date: {date_str}")
        return "Value Error", "Value Error"
    except Exception as e:
        print(f"Unexpected date conversion error: {str(e)} for date: {date_str}")
        return "Value Error", "Value Error"

def convert_children_dates(children_list: list) -> list:
    """
    Convert birth dates for a list of children from Hijri to include both Hijri and Gregorian.

    Args:
        children_list (list): List of dictionaries containing children's information

    Returns:
        list: Updated list with converted dates
    """
    try:
        converted_list = []

        for child in children_list:
            # Create a copy of the child dict to avoid modifying the original
            child_info = child.copy()

            # Get the original Hijri date
            hijri_date = child_info['child_birth_date']

            try:
                # Convert the date using the previous function
                _, gregorian_str = convert_date(hijri_date, 'h2g')

                # Check if conversion failed
                if gregorian_str == "Value Error":
                    child_info['child_birth_date'] = f"{hijri_date} H\nValue Error G"
                else:
                    # Format the combined date string
                    child_info['child_birth_date'] = f"{hijri_date} H\n{gregorian_str} G"

            except Exception as e:
                # If there's an error converting the date, keep the original
                print(f"Warning: Could not convert date for {child_info['child_name']}: {e}")
                child_info['child_birth_date'] = f"{hijri_date} H\nValue Error G"

            converted_list.append(child_info)

        return converted_list

    except Exception as e:
        print(f"Error processing children list: {str(e)}")
        return converted_list

def format_trademark_info(json_string):
    # Initialize empty list to store lines
    """
    Format trademark information from JSON string into a Markdown string.

    Args:
        json_string (str): JSON string containing trademark information

    Returns:
        str: Formatted Markdown string

    Example:
        >>> format_trademark_info(
        ... {
        ...     "tm_language": "ARABIC",
        ...     "en_name": "TEST",
        ...     "ar_name": "",
        ...     "description": "TEST DESCRIPTION"
        ... }
        ... )
        "*المعلومات الشكلية للعلامة التجارية*\n\n*لغة العلامة:* ARABIC\n\n*الاسم اللاتيني:* TEST\n\n*الاسم العربي:* \n\n*الوصف:*\nTEST DESCRIPTION\n\n*نوع العلامة من حيث الوصف:* لفظية وتصويرية\n"
    """
    lines = ["*المعلومات الشكلية للعلامة التجارية*", ""]

    # Add language line
    lines.append(f"*لغة العلامة:* {json_string['tm_language']}")
    lines.append("")

    # Add Latin name if it exists
    if 'en_name' in json_string and json_string['en_name']:
        lines.append(f"*الاسم اللاتيني:* {json_string['en_name']}")
        lines.append("")

    # Add Arabic name if it exists
    if 'ar_name' in json_string and json_string['ar_name']:
        lines.append(f"*الاسم العربي:* {json_string['ar_name']}")
        lines.append("")

    # Add description
    lines.append("*الوصف:*")
    lines.append(json_string['description'])
    lines.append("")

    # Add trademark type
    lines.append("*نوع العلامة من حيث الوصف:* لفظية وتصويرية")

    # Join all lines with newlines
    full_text = "\n".join(lines)

    office_text = ["*وصف العلامة التجارية:*", ""]

    office_text.append(json_string['description'])
    office_text.append("")
    office_text.append("")
    office_text.append("لتسجيل علامتك بشكل صحيح ننصحك بمشاهدة هذا المقطع 👇🏻")
    office_text.append("https://alama.com.co/registration-video")
    office_text.append("")
    office_text.append("لتجنب الرفض النهائي والتأخير في تسجيل علامتك تفضل بطلب خدمة تسجيل العلامة التجارية بشكل كامل:")
    office_text.append("https://alama.com.co/trademark-registration/")


    office_text = "\n".join(office_text)

    return full_text, office_text

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def convert_to_json(ai_response):

    # Look for text between curly braces
    # This pattern matches balanced curly braces
    pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    
    try:
        match = re.search(pattern, ai_response, re.DOTALL)
        if match:
            json_str = match.group(0)
            return json.loads(json_str)
    except json.JSONDecodeError:
        print("Found text looks like JSON but isn't valid JSON")
    except Exception as e:
        print(f"Error: {e}")
    return None

def print_word_tables(document_path):

    # Load the document
    doc = Document(document_path)

    # Iterate through each table in the document
    for i, table in enumerate(doc.tables):
        print(f"Table {i + 1}:")
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                row_data.append(cell.text.strip())
            print("\t" + "\t".join(row_data))
        print()  # Add a newline for better readability between tables

def perform_ocr(image_path: str) -> List[str]:
    """
    Perform OCR on an image using Google Cloud Vision API with caching.

    Args:
        image_path (str): Path to the image file.

    Returns:
        A tuple of two lists of strings. The first list contains the grouped text lines
        and the second list contains the original text lines from the OCR response.

    Raises:
        Exception: If the API call fails.
    """
    # Check cache first
    cached_result = get_cached_vision_result(image_path, "text_detection")

    if cached_result:
        # Return cached result
        result_data = cached_result['result']
        return result_data['lines'], result_data['original_lines']

    # Make API call if not cached
    client = vision.ImageAnnotatorClient()

    with open(image_path, 'rb') as image_file:
        content = image_file.read()

    image_context = vision.ImageContext(language_hints=['ar', 'en'])
    image = vision.Image(content=content)
    response = client.text_detection(image=image, image_context=image_context)
    original_lines = response.full_text_annotation.text.split('\n')

    if response.error.message:
        raise Exception(f'{response.error.message}\nFor more info on error messages, check: '
                        'https://cloud.google.com/apis/design/errors')

    # Extract text annotations
    text_annotations = response.text_annotations[1:]  # Skip the first one, which is the entire text

    # Group words into lines
    lines = group_into_lines(text_annotations)

    # Cache the result
    cache_data = {
        'lines': lines,
        'original_lines': original_lines
    }
    cache_vision_result(image_path, cache_data, "text_detection")

    return lines, original_lines

def group_into_lines(annotations: List[vision.TextAnnotation]) -> List[str]:
  # Sort annotations by y-coordinate (top to bottom)
  sorted_annotations = sorted(annotations, key=lambda a: a.bounding_poly.vertices[0].y)

  lines = []
  current_line = []
  previous_y = None
  tolerance = 10  # Adjust this value to change the grouping sensitivity

  for annotation in sorted_annotations:
      current_y = annotation.bounding_poly.vertices[0].y

      if previous_y is None or abs(current_y - previous_y) <= tolerance:
          current_line.append(annotation)
      else:
          # Sort the current line by x-coordinate (right to left for RTL)
          current_line.sort(key=lambda a: -a.bounding_poly.vertices[0].x)
          lines.append(" ".join(a.description for a in current_line))
          current_line = [annotation]

      previous_y = current_y

  if current_line:
      # Sort the last line
      current_line.sort(key=lambda a: -a.bounding_poly.vertices[0].x)
      lines.append(" ".join(a.description for a in current_line))

  return lines

def find_keyword_line(lines, keyword, expected_length=None):
    matching_lines = []
    
    for i in range(len(lines)):
        # Get the current line and convert it to lowercase for case-insensitive matching
        current_line = lines[i]
        lowercase_line = current_line.lower()

        # Check if the keyword is in the current line
        if keyword.lower() in lowercase_line:
            # Remove leading and trailing whitespace
            current_line = current_line.strip()

            # If expected_length is provided and the current line is shorter than expected,
            # concatenate with the next line (if it exists)
            if expected_length and len(current_line) < expected_length and i + 1 < len(lines):
                next_line = lines[i + 1].lower().strip()  # Get the next line and strip it
                current_line += ' ' + next_line  # Concatenate the current line with the next one

            matching_lines.append(current_line)

    if len(matching_lines) > 0:
        return matching_lines[0]
    else:
        return ""
 
def fill_word_template(data_dict, template_path, output_path, default_image_width=2.0, default_image_height=2.5):
    #   print(data_dict)
    # Load the Word document
  doc = Document(template_path)
  
  # Add Arabic language support to the document defaults
  settings = doc.settings._element
  bidi = OxmlElement('w:bidi')
  settings.append(bidi)

  def is_arabic(text):
      # Check if the text contains Arabic characters
      arabic_ranges = [(0x0600, 0x06FF), (0x0750, 0x077F), (0x08A0, 0x08FF),
                      (0xFB50, 0xFDFF), (0xFE70, 0xFEFF)]
      return any(any(ord(char) >= r[0] and ord(char) <= r[1] for r in arabic_ranges)
                for char in str(text))

  def is_image_path(data):
      # Check if the data is a string that looks like an image file path
      if not isinstance(data, str):
          return False

      # Check if it's a file path with image extension
      image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']
      data_lower = data.lower()

      # Check if it has an image extension and exists as a file
      if any(data_lower.endswith(ext) for ext in image_extensions):
          return os.path.exists(data)

      return False

  def write_to_cell(table, row, col, data, image_width_inches=None, image_height_inches=None):
      # ========== IMAGE SIZE SETTINGS - EASY TO MODIFY ==========
      # Person photo sizes (for face photos, portraits, personal images)
      PERSON_PHOTO_WIDTH = 1.4   # inches
      PERSON_PHOTO_HEIGHT = 1.8  # inches

      # Document image sizes (for ID cards, certificates, official documents)
      DOCUMENT_IMAGE_WIDTH = 6.5   # inches
      DOCUMENT_IMAGE_HEIGHT = 6  # inches
      # =========================================================

      try:
          cell = table.cell(row, col)

          # Check if data is an image path
          if is_image_path(data):
              # Set intelligent defaults based on image type if not specified
              if image_width_inches is None or image_height_inches is None:
                  # Check if this looks like a person photo (common naming patterns)
                  data_lower = str(data).lower()
                  if any(keyword in data_lower for keyword in ['person', 'photo', 'face', 'portrait', 'personal']):
                      # Person photo defaults - smaller, portrait oriented
                      default_width, default_height = PERSON_PHOTO_WIDTH, PERSON_PHOTO_HEIGHT
                  else:
                      # Document image defaults - larger, landscape oriented
                      default_width, default_height = DOCUMENT_IMAGE_WIDTH, DOCUMENT_IMAGE_HEIGHT

                  image_width_inches = image_width_inches or default_width
                  image_height_inches = image_height_inches or default_height
              # Handle image insertion
              try:
                  # Clear existing content
                  cell.text = ""

                  # Get the first paragraph in the cell
                  paragraph = cell.paragraphs[0]

                  # Add the image to the paragraph
                  run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()

                  # Calculate dimensions that preserve aspect ratio
                  # Get original image dimensions to calculate aspect ratio
                  with Image.open(data) as img:
                      original_width, original_height = img.size
                      original_aspect_ratio = original_width / original_height

                  # Calculate display dimensions that preserve aspect ratio
                  # Use the smaller dimension to ensure image fits within specified bounds
                  target_aspect_ratio = image_width_inches / image_height_inches

                  if original_aspect_ratio > target_aspect_ratio:
                      # Image is wider than target - constrain by width
                      display_width = image_width_inches
                      display_height = image_width_inches / original_aspect_ratio
                  else:
                      # Image is taller than target - constrain by height
                      display_height = image_height_inches
                      display_width = image_height_inches * original_aspect_ratio

                  # Add image with calculated dimensions (preserves aspect ratio and original resolution)
                  run.add_picture(data, width=Inches(display_width), height=Inches(display_height))

                  # Center align the paragraph containing the image
                  paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                  print(f"Successfully added image to cell ({row}, {col}) with display size {display_width:.2f}x{display_height:.2f} inches (preserving aspect ratio): {os.path.basename(data)}")
                  return

              except Exception as img_error:
                  print(f"Error adding image to cell ({row}, {col}): {str(img_error)}")
                  # Fall back to text if image insertion fails
                  data = f"[Image: {os.path.basename(data)}]"

          # Handle text data (original logic)
          # Store the existing paragraph's formatting
          existing_paragraph = cell.paragraphs[0]
          existing_style = existing_paragraph.style
          existing_alignment = existing_paragraph.alignment

          # Store the existing run's formatting
          if existing_paragraph.runs:
              existing_run = existing_paragraph.runs[0]
              existing_font = existing_run.font
              existing_font_name = existing_font.name
              existing_font_size = existing_font.size
              existing_font_bold = existing_font.bold
          else:
              existing_font_name = 'Calibri'
              existing_font_size = Pt(14)
              existing_font_bold = True

          # Clear existing content
          cell.text = ""

          # Get the first paragraph in the cell
          paragraph = cell.paragraphs[0]
          
          # Set paragraph properties
          pPr = paragraph._p.get_or_add_pPr()
          
          # Create run
          run = paragraph.add_run(str(data))
          
          # Set run properties
          rPr = run._r.get_or_add_rPr()
          
          # Check if text contains Arabic
          if is_arabic(str(data)):
              # Add bidirectional text support for Arabic
              bidi = OxmlElement('w:bidi')
              pPr.append(bidi)
              
              # Set RTL for the run
              rtl = OxmlElement('w:rtl')
              rPr.append(rtl)
              
              # Set language for the run
              lang = OxmlElement('w:lang')
              lang.set(qn('w:bidi'), 'ar-SA')
              rPr.append(lang)
              
              # Right align Arabic text
            #   paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
          else:
              # Left align non-Arabic text
              paragraph.alignment = existing_alignment
          
          # Apply formatting - try to use existing formatting, fall back to defaults if needed
          try:
              run.font.name = existing_font_name
              run.font.size = existing_font_size
              run.font.bold = existing_font_bold
          except:
              run.font.name = 'Calibri'
              run.font.size = Pt(14)
              run.font.bold = True
          
          # Apply style
          try:
              paragraph.style = existing_style
          except:
              paragraph.style = doc.styles['Normal']

      except IndexError:
          print(f"Error: Unable to access row {row}, column {col}. Check if they exist.")

  # Iterate through the data dictionary
  for key, value in data_dict.items():
      # Support both 4-element and 6-element formats
      # 4-element: [data, table_index, start_row, start_col]
      # 6-element: [data, table_index, start_row, start_col, image_width, image_height]
      if len(value) not in [4, 6]:
          print(f"Warning: Skipping invalid data for key '{key}'. Expected 4 or 6 elements, got {len(value)}.")
          continue

      if len(value) == 4:
          data, table_index, start_row, start_col = value
          image_width, image_height = default_image_width, default_image_height
      else:
          data, table_index, start_row, start_col, image_width, image_height = value

      table_index, start_row, start_col = int(table_index), int(start_row), int(start_col)
      image_width, image_height = float(image_width), float(image_height)

      try:
          table = doc.tables[table_index]

          if isinstance(data, list) and all(isinstance(item, dict) for item in data):
              # Handle list of objects
              for row_offset, obj in enumerate(data):
                  for col_offset, (attr, attr_value) in enumerate(obj.items()):
                      write_to_cell(table, start_row + row_offset, start_col + col_offset, attr_value)
          else:
              # Handle simple data
              write_to_cell(table, start_row, start_col, data)

      except IndexError:
          print(f"Error: Unable to access table {table_index} for key '{key}'. Check if it exists.")

  # Save the modified document
  doc.save(output_path)
  print(f"Document successfully saved to {output_path}")
  return output_path

def convert_pdf_to_image(pdf_path, output_folder=None, quality=95, debugging=False):
    """
    Convert PDF to JPG images using pdf2image library.

    Args:
        pdf_path (str): Path to the PDF file
        output_folder (str, optional): Folder to save converted images. If None, saves in same directory as PDF
        quality (int): JPEG quality (1-100, default 95)
        debugging (bool): Enable debug prints (default: False)

    Returns:
        list: List of paths to converted image files
    """
    try:
        from pdf2image import convert_from_path

        if debugging:
            print(f"=== PDF CONVERSION DEBUG ===")
            print(f"PDF path: {pdf_path}")
            print(f"Output folder: {output_folder}")

        # Validate PDF file exists and is not empty
        if not os.path.exists(pdf_path):
            if debugging:
                print(f"ERROR: PDF file does not exist: {pdf_path}")
            return []

        file_size = os.path.getsize(pdf_path)
        if file_size == 0:
            if debugging:
                print(f"ERROR: PDF file is empty: {pdf_path}")
            return []

        if debugging:
            print(f"✓ PDF file exists and has size: {file_size} bytes")

        # Determine output folder
        if output_folder is None:
            output_folder = os.path.dirname(pdf_path)

        if debugging:
            print(f"Using output folder: {output_folder}")

        # Ensure output folder exists
        os.makedirs(output_folder, exist_ok=True)
        if debugging:
            print(f"✓ Output folder created/verified: {output_folder}")

        # Generate safe base filename (avoid encoding issues)
        import time
        import uuid
        safe_base_name = f"pdf_converted_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        if debugging:
            print(f"Safe base filename: {safe_base_name}")
            print(f"Converting PDF: {os.path.basename(pdf_path)} (size: {file_size} bytes)")

        # Convert PDF to images with error handling
        try:
            if debugging:
                print("Attempting PDF conversion with DPI=200, format=JPEG...")
            images = convert_from_path(pdf_path, dpi=200, fmt='jpeg')
            if debugging:
                print(f"✓ PDF conversion successful, got {len(images)} pages")
        except Exception as pdf_error:
            if debugging:
                print(f"✗ PDF conversion failed: {str(pdf_error)}")
                print(f"Error type: {type(pdf_error).__name__}")
            # Try with different parameters
            try:
                if debugging:
                    print("Retrying with DPI=150, format=PNG...")
                images = convert_from_path(pdf_path, dpi=150, fmt='png')
                if debugging:
                    print(f"✓ Retry successful, got {len(images)} pages")
            except Exception as retry_error:
                if debugging:
                    print(f"✗ Retry also failed: {str(retry_error)}")
                    print(f"Retry error type: {type(retry_error).__name__}")
                return []

        if not images:
            if debugging:
                print("✗ No images were extracted from PDF")
            return []

        if debugging:
            print(f"✓ Extracted {len(images)} images from PDF")
        converted_paths = []

        # Save each page as a separate JPG file
        for i, image in enumerate(images, start=1):
            if len(images) == 1:
                # Single page PDF - no page number suffix
                output_path = os.path.join(output_folder, f"{safe_base_name}.jpg")
            else:
                # Multi-page PDF - add page number
                output_path = os.path.join(output_folder, f"{safe_base_name}_page_{i}.jpg")

            if debugging:
                print(f"Saving page {i} to: {output_path}")

            try:
                # Save with specified quality
                image.save(output_path, 'JPEG', quality=quality, optimize=True)

                # Verify the saved file
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    converted_paths.append(output_path)
                    if debugging:
                        print(f"✓ Converted PDF page {i} to: {os.path.basename(output_path)} ({os.path.getsize(output_path)} bytes)")
                else:
                    if debugging:
                        print(f"✗ Failed to save page {i} - file missing or empty")

            except Exception as save_error:
                if debugging:
                    print(f"✗ Error saving page {i}: {str(save_error)}")
                    print(f"Save error type: {type(save_error).__name__}")
                continue

        if debugging:
            print(f"✓ PDF conversion completed: {len(converted_paths)} images saved")
        return converted_paths

    except ImportError:
        if debugging:
            print("Error: pdf2image library not found. Please install it with: pip install pdf2image")
        return []
    except Exception as e:
        if debugging:
            print(f"Error converting PDF to image: {str(e)}")
        return []

def compress_img(img_path, quality=95, debugging=False):
    """
    Compress an image while maintaining good quality.

    Args:
        img_path (str): Path to the image file
        quality (int): JPEG quality (1-100, default: 95 for high quality)
        debugging (bool): Enable debug prints (default: False)

    Returns:
        str: Path to the optimized image (same as input path)
    """
    try:
        # Get file extension to preserve original format
        file_extension = os.path.splitext(img_path)[1].lower()

        # Get original file size for comparison
        original_size = os.path.getsize(img_path)

        with Image.open(img_path) as img:
            # Convert RGBA to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create a white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode not in ('RGB', 'L'):
                img = img.convert('RGB')

            if file_extension in ['.jpg', '.jpeg']:
                # For JPEG files, use specified quality with optimization
                img.save(img_path, 'JPEG', quality=quality, optimize=True)
                if debugging:
                    new_size = os.path.getsize(img_path)
                    compression_ratio = (1 - new_size / original_size) * 100
                    print(f"Compressed JPEG image: {img_path}")
                    print(f"  Original size: {original_size:,} bytes")
                    print(f"  New size: {new_size:,} bytes")
                    print(f"  Compression: {compression_ratio:.1f}%")
                    print(f"  Quality: {quality}")
            elif file_extension == '.png':
                # For PNG files, apply optimize flag for lossless compression
                img.save(img_path, 'PNG', optimize=True)
                if debugging:
                    new_size = os.path.getsize(img_path)
                    compression_ratio = (1 - new_size / original_size) * 100
                    print(f"Optimized PNG image: {img_path}")
                    print(f"  Original size: {original_size:,} bytes")
                    print(f"  New size: {new_size:,} bytes")
                    print(f"  Compression: {compression_ratio:.1f}%")
            else:
                # For other formats, convert to JPEG with high quality
                base_name = os.path.splitext(img_path)[0]
                new_path = f"{base_name}.jpg"
                img.save(new_path, 'JPEG', quality=quality, optimize=True)

                # Remove original file and rename new file to original name
                if new_path != img_path:
                    os.remove(img_path)
                    os.rename(new_path, img_path)

                if debugging:
                    new_size = os.path.getsize(img_path)
                    compression_ratio = (1 - new_size / original_size) * 100
                    print(f"Converted {file_extension} to JPEG: {img_path}")
                    print(f"  Original size: {original_size:,} bytes")
                    print(f"  New size: {new_size:,} bytes")
                    print(f"  Compression: {compression_ratio:.1f}%")
                    print(f"  Quality: {quality}")

            return img_path

    except Exception as e:
        if debugging:
            print(f"Error compressing image {img_path}: {str(e)}")
        return img_path  # Return original path if compression fails
