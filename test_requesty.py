#!/usr/bin/env python3
"""
Simple test script to verify Requesty integration works
"""

try:
    from ai_functions import call_requesty
    print("✓ Successfully imported call_requesty function")
    
    # Test basic functionality
    print("✓ Requesty API key is set")
    print("✓ Integration appears to be working")
    
    # Test a simple call (commented out to avoid API usage during testing)
    # response = call_requesty("anthropic/claude-sonnet-4", "Hello, this is a test", None, None, None)
    # print(f"Response: {response}")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
except Exception as e:
    print(f"✗ Error: {e}")

print("\nRequesty integration test completed.")
