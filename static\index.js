/**
 * Swift Switch - Frontend JavaScript
 *
 * Optimized version with improved structure, performance, and maintainability.
 * Handles user interface interactions including file uploads, form validation,
 * dynamic UI updates, and notifications.
 *
 * Also handles translation settings updates via AJAX.
 */

// Main application namespace to avoid global scope pollution
const SwiftSwitch = (function() {
    'use strict';

    // DOM element cache - stored in a single object for better organization
    const elements = {};

    // State tracking
    let lastHoveredDropArea = null;

    /**
     * Initialize the application
     * Entry point for the application
     */
    function init() {
        cacheElements();
        initializeEventListeners();
        initializeTranslationSettings();
        restorePastedFiles();
        checkForSuccessfulCompletion();
    }

    /**
     * Restore pasted files from localStorage on page load
     */
    function restorePastedFiles() {
        const restoredFiles = [];
        const keysToRemove = [];

        // Look for stored pasted files in localStorage
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('pastedFile_')) {
                try {
                    const fileData = JSON.parse(localStorage.getItem(key));

                    // Check if file is not too old (remove files older than 24 hours)
                    const ageInHours = (Date.now() - fileData.timestamp) / (1000 * 60 * 60);
                    if (ageInHours > 24) {
                        keysToRemove.push(key);
                        continue;
                    }

                    // Convert base64 data URL back to File object
                    fetch(fileData.data)
                        .then(res => res.blob())
                        .then(blob => {
                            const restoredFile = new File([blob], fileData.name, {
                                type: fileData.type,
                                lastModified: fileData.timestamp
                            });
                            restoredFiles.push(restoredFile);

                            // If this is the last file to restore, add them all to the file input
                            if (restoredFiles.length === getValidPastedFileCount()) {
                                if (restoredFiles.length > 0) {
                                    const dataTransfer = new DataTransfer();
                                    restoredFiles.forEach(file => dataTransfer.items.add(file));

                                    // Add to main file input
                                    if (elements.fileInput) {
                                        elements.fileInput.files = dataTransfer.files;
                                        handleFiles(dataTransfer.files, false); // Don't append, replace
                                    }
                                }
                            }
                        })
                        .catch(error => {
                            console.warn('Could not restore pasted file:', error);
                            keysToRemove.push(key);
                        });
                } catch (error) {
                    console.warn('Could not parse stored file data:', error);
                    keysToRemove.push(key);
                }
            }
        }

        // Clean up old or invalid files
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    /**
     * Check if the page shows successful completion results and auto-clear upload area
     */
    function checkForSuccessfulCompletion() {
        // Check for various indicators that a request was completed successfully
        const hasResults = document.querySelector('.results-container');
        const hasSuccessMessage = document.querySelector('.success-message');
        const hasAiResponse = document.querySelector('.ai-response');
        const hasOcrResults = document.querySelector('#json-renderer');
        const hasOriginalLines = document.querySelector('.original-lines');

        // If any results are present, it means a request was completed successfully
        if (hasResults || hasSuccessMessage || hasAiResponse || hasOcrResults || hasOriginalLines) {
            console.log('Successful completion detected, clearing upload area...');

            // Small delay to ensure the page is fully rendered before clearing
            setTimeout(() => {
                clearAllFiles();
                showNotification('Upload area cleared after successful completion', 'info', 'Auto-Clear');
            }, 500);
        }
    }

    /**
     * Count valid pasted files in localStorage
     */
    function getValidPastedFileCount() {
        let count = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('pastedFile_')) {
                try {
                    const fileData = JSON.parse(localStorage.getItem(key));
                    const ageInHours = (Date.now() - fileData.timestamp) / (1000 * 60 * 60);
                    if (ageInHours <= 24) {
                        count++;
                    }
                } catch (error) {
                    // Invalid data, will be cleaned up
                }
            }
        }
        return count;
    }

    /**
     * Cache DOM elements for better performance
     * Stores references to frequently accessed elements
     */
    function cacheElements() {
        // Main file upload elements
        elements.dropArea = document.getElementById('drop-area');
        elements.fileInput = document.getElementById('fileElem');
        elements.fileNameDisplay = document.getElementById('file-name');
        elements.previewArea = document.getElementById('preview-area');
        elements.clearFilesBtn = document.getElementById('clear-files-btn');

        // Second page upload elements
        elements.secondDropArea = document.getElementById('second-drop-area');
        elements.secondFileInput = document.getElementById('secondFileElem');
        elements.secondFileNameDisplay = document.getElementById('second-file-name');
        elements.secondPageUpload = document.getElementById('second-page-upload');

        // Form and error handling
        elements.form = document.getElementById('upload-form');
        elements.errorMessage = document.getElementById('error-message');

        // Translation settings elements
        elements.documentTranslationProvider = document.getElementById('document_translation_provider');
        elements.tmTranslationProvider = document.getElementById('tm_translation_provider');
        elements.openrouterModel = document.getElementById('openrouter_model');
        elements.requestyModel = document.getElementById('requesty_model');
        elements.saveSettingsBtn = document.getElementById('save_settings');

        // Document type sub-options
        elements.idTypes = document.getElementById('id-types');
        elements.tmTypes = document.getElementById('tm-types');
        elements.iqamaTypes = document.getElementById('iqama-types');
        elements.familyCardTypes = document.getElementById('family-card-types');
    }

    /**
     * Sets up all event listeners for the application
     */
    function initializeEventListeners() {
        setupDropAreas();
        setupPasteEventListener();
        setupFormSubmission();
        setupRadioButtons();
        setupClearButton();
    }

    /**
     * Sets up the drag and drop functionality for file upload areas
     */
    function setupDropAreas() {
        // Configure both drop areas with common event handlers
        [elements.dropArea, elements.secondDropArea].forEach(area => {
            if (area) {
                setupDropArea(area);
            }
        });

        // Main drop area specific handlers
        if (elements.dropArea) {
            elements.dropArea.addEventListener('drop', handleDrop);
            elements.dropArea.addEventListener('click', () => elements.fileInput.click());
            elements.fileInput.addEventListener('change', () => handleFiles(elements.fileInput.files));
        }

        // Second drop area specific handlers
        if (elements.secondDropArea) {
            elements.secondDropArea.addEventListener('drop', handleSecondDrop);
            elements.secondDropArea.addEventListener('click', () => elements.secondFileInput.click());
            elements.secondFileInput.addEventListener('change', () => handleSecondFiles(elements.secondFileInput.files));
        }
    }

    /**
     * Configures a single drop area with all necessary event listeners
     * @param {HTMLElement} area - The drop area element to configure
     */
    function setupDropArea(area) {
        // Prevent default browser behavior for drag events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, preventDefaults);
        });

        // Add highlight class when dragging over the area
        ['dragenter', 'dragover'].forEach(eventName => {
            area.addEventListener(eventName, () => area.classList.add('highlight'));
        });

        // Remove highlight class when leaving the area or after drop
        ['dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, () => area.classList.remove('highlight'));
        });

        // Track which drop area was last hovered (for paste operations)
        area.addEventListener('mouseenter', () => lastHoveredDropArea = area);
        area.addEventListener('mouseleave', () => {
            if (lastHoveredDropArea === area) lastHoveredDropArea = null;
        });
    }

    /**
     * Sets up clipboard paste event listener for the entire document
     */
    function setupPasteEventListener() {
        document.addEventListener('paste', handlePaste);
    }

    /**
     * Sets up form validation and submission handling
     */
    function setupFormSubmission() {
        if (!elements.form) return;

        elements.form.addEventListener('submit', function(e) {
            if (!validateForm(e)) {
                return false;
            }

            // If valid, collect external fields before submission
            collectExternalFields();
        });
    }

    /**
     * Sets up the clear files button functionality
     */
    function setupClearButton() {
        if (elements.clearFilesBtn) {
            elements.clearFilesBtn.addEventListener('click', clearAllFiles);
        }
    }

    /**
     * Validates the form before submission
     * @param {Event} e - The form submission event
     * @returns {boolean} - Whether the form is valid
     */
    function validateForm(e) {
        let isValid = true;

        // Validate: File must be selected
        if (!elements.fileInput.files.length) {
            e.preventDefault();
            showError('Please select a file before uploading.');
            isValid = false;
        }

        // Validate: Document type must be selected
        const selectedType = document.querySelector('input[name="document-type"]:checked');
        if (!selectedType) {
            e.preventDefault();
            showError('Please select a document type.');
            isValid = false;
        }

        // Validate: If family card with two pages is selected, second page must be provided
        if (selectedType && selectedType.value === 'family-card') {
            const familyCardType = document.querySelector('input[name="family-card-type"]:checked');
            if (familyCardType &&
                (familyCardType.value === 'two-pages' || familyCardType.value === 'two-pages-new') &&
                !elements.secondFileInput.files.length) {
                e.preventDefault();
                showError('Please select a file for the second page.');
                isValid = false;
            }
        }

        return isValid;
    }

    /**
     * Sets up event listeners for radio button selections
     */
    function setupRadioButtons() {
        // Add change event listeners to all document type radio buttons
        const documentTypeRadios = document.querySelectorAll('input[name="document-type"]');
        documentTypeRadios.forEach(radio => {
            radio.addEventListener('change', handleDocumentTypeChange);
        });

        // Add change event listeners to all family card type radio buttons
        document.querySelectorAll('input[name="family-card-type"]').forEach(radio => {
            radio.addEventListener('change', handleFamilyCardTypeChange);
        });
    }

    /**
     * Prevents default browser behavior for events
     * @param {Event} e - The event object
     */
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * Handles paste events from the clipboard
     * @param {ClipboardEvent} e - The clipboard event object
     */
    function handlePaste(e) {
        const items = e.clipboardData.items;
        const pastedFiles = [];
        let processedCount = 0;
        let totalImageCount = 0;

        // First, count how many images we have
        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                totalImageCount++;
            }
        }

        if (totalImageCount === 0) {
            return; // No images to process
        }

        // Process all images
        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                // Get the blob from clipboard
                const blob = items[i].getAsFile();

                // Create a proper file with a unique name and save the data immediately
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const fileName = `pasted-image-${timestamp}-${i}.png`;

                // Create a new File object with the blob data
                // This ensures the data is copied and not just referenced
                const reader = new FileReader();
                reader.onload = function(event) {
                    // Convert the data URL back to a blob to create a proper file
                    fetch(event.target.result)
                        .then(res => res.blob())
                        .then(savedBlob => {
                            // Create a new file with the saved blob data and persistent storage
                            const savedFile = new File([savedBlob], fileName, {
                                type: blob.type || 'image/png',
                                lastModified: Date.now()
                            });

                            // Store file data in localStorage for persistence across page refreshes
                            try {
                                const fileData = {
                                    name: fileName,
                                    type: savedFile.type,
                                    data: event.target.result, // base64 data URL
                                    timestamp: Date.now()
                                };
                                localStorage.setItem(`pastedFile_${fileName}`, JSON.stringify(fileData));
                            } catch (error) {
                                console.warn('Could not store file in localStorage:', error);
                            }

                            pastedFiles.push(savedFile);
                            processedCount++;

                            // When all files are processed, add them to the appropriate drop area
                            if (processedCount === totalImageCount) {
                                // Create a DataTransfer object with all saved files
                                const files = new DataTransfer();
                                pastedFiles.forEach(file => files.items.add(file));

                                // Route to the appropriate handler based on which area was last hovered
                                // Always append pasted files to existing ones
                                if (lastHoveredDropArea === elements.secondDropArea) {
                                    handleSecondFiles(files.files, true); // append = true
                                } else {
                                    handleFiles(files.files, true); // append = true
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error saving pasted image:', error);
                            showError('Failed to process pasted image');
                        });
                };

                // Read the blob as data URL to save the image data
                reader.readAsDataURL(blob);
            }
        }
    }

    /**
     * Handles file drop events for the main drop area
     * @param {DragEvent} e - The drag event object
     */
    function handleDrop(e) {
        const files = e.dataTransfer.files;
        handleFiles(files);
    }

    /**
     * Handles file drop events for the second drop area
     * @param {DragEvent} e - The drag event object
     */
    function handleSecondDrop(e) {
        const files = e.dataTransfer.files;
        handleSecondFiles(files);
    }

    /**
     * Processes files for the main drop area
     * @param {FileList} files - The list of files to process
     * @param {boolean} append - Whether to append to existing files or replace them
     */
    function handleFiles(files, append = false) {
        // Get current files if appending
        let currentFiles = [];
        if (append && elements.fileInput.files) {
            currentFiles = Array.from(elements.fileInput.files);
        }

        // Add new files to current files
        const newFiles = Array.from(files);
        const allFiles = append ? [...currentFiles, ...newFiles] : newFiles;

        // Clear displays only if not appending
        if (!append) {
            elements.fileNameDisplay.textContent = '';
            elements.previewArea.innerHTML = '';
        }

        // Create a new DataTransfer object to update the file input
        const dataTransfer = new DataTransfer();
        allFiles.forEach(file => dataTransfer.items.add(file));
        elements.fileInput.files = dataTransfer.files;

        // Display file names - show only new files when appending
        if (!append) {
            elements.fileNameDisplay.innerHTML = '';
            // Show all files when not appending
            allFiles.forEach(file => {
                elements.fileNameDisplay.innerHTML += `<div>Selected file: ${file.name}</div>`;
            });
        } else {
            // Show only new files when appending
            newFiles.forEach(file => {
                elements.fileNameDisplay.innerHTML += `<div>Selected file: ${file.name}</div>`;
            });
        }

        // Show/hide clear button based on whether there are files
        if (elements.clearFilesBtn) {
            elements.clearFilesBtn.style.display = allFiles.length > 0 ? 'inline-block' : 'none';
        }

        // Generate previews for all new files (when appending) or all files (when not appending)
        const filesToPreview = append ? newFiles : allFiles;
        filesToPreview.forEach(file => {
            if (file.type.startsWith('image/')) {
                createImagePreview(file);
            } else {
                createFileIconPreview(file);
            }
        });
    }

    /**
     * Creates an image preview for image files in horizontal layout
     * @param {File} file - The image file to preview
     */
    function createImagePreview(file) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = function() {
            // Create a container for the image (no filename display)
            const imageContainer = document.createElement('div');
            imageContainer.className = 'preview-image-container';
            imageContainer.innerHTML = `
                <img src="${reader.result}"
                     alt="File preview"
                     class="preview-image"
                     title="${file.name}">`;
            elements.previewArea.appendChild(imageContainer);
        };
    }

    /**
     * Creates a file icon preview for non-image files in horizontal layout
     * @param {File} file - The file to create an icon for
     */
    function createFileIconPreview(file) {
        const extension = file.name.split('.').pop().toUpperCase();
        // Create a container for the file icon (no filename display)
        const iconContainer = document.createElement('div');
        iconContainer.className = 'preview-image-container';
        iconContainer.innerHTML = `
            <div class="file-icon" title="${file.name}">${extension}</div>`;
        elements.previewArea.appendChild(iconContainer);
    }

    /**
     * Processes files for the second drop area
     * @param {FileList} files - The list of files to process
     * @param {boolean} append - Whether to append to existing files or replace them
     */
    function handleSecondFiles(files, append = false) {
        if (files.length > 0) {
            // Get current files if appending
            let currentFiles = [];
            if (append && elements.secondFileInput.files) {
                currentFiles = Array.from(elements.secondFileInput.files);
            }

            // Filter only image files
            const newImageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

            if (newImageFiles.length === 0) {
                showError('Please select an image file for the second page.');
                return;
            }

            // Add new files to current files
            const allFiles = append ? [...currentFiles, ...newImageFiles] : newImageFiles;

            // Create a new DataTransfer object to update the file input
            const dataTransfer = new DataTransfer();
            allFiles.forEach(file => dataTransfer.items.add(file));
            elements.secondFileInput.files = dataTransfer.files;

            // Update display - show only new files when appending
            if (!append) {
                elements.secondFileNameDisplay.textContent = '';
                // Show all files when not appending
                allFiles.forEach(file => {
                    elements.secondFileNameDisplay.innerHTML += `<div>Selected file: ${file.name}</div>`;
                });
            } else {
                // Show only new files when appending
                newImageFiles.forEach(file => {
                    elements.secondFileNameDisplay.innerHTML += `<div>Selected file: ${file.name}</div>`;
                });
            }
        }
    }

    /**
     * Clears all uploaded files from the main drop area
     */
    function clearAllFiles() {
        // Clear file input
        if (elements.fileInput) {
            elements.fileInput.value = '';
            const dataTransfer = new DataTransfer();
            elements.fileInput.files = dataTransfer.files;
        }

        // Clear displays
        if (elements.fileNameDisplay) {
            elements.fileNameDisplay.innerHTML = '';
        }
        if (elements.previewArea) {
            elements.previewArea.innerHTML = '';
        }

        // Hide clear button
        if (elements.clearFilesBtn) {
            elements.clearFilesBtn.style.display = 'none';
        }

        // Clear pasted files from localStorage
        clearPastedFilesFromStorage();
    }

    /**
     * Clear all pasted files from localStorage
     */
    function clearPastedFilesFromStorage() {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('pastedFile_')) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    /**
     * Displays an error message and resets the file input
     * @param {string} message - The error message to display
     */
    function showError(message) {
        // Show the error message
        elements.errorMessage.textContent = message;
        elements.errorMessage.style.display = 'block';

        // Reset the file input and preview areas
        elements.fileNameDisplay.textContent = '';
        elements.previewArea.innerHTML = '';
        elements.fileInput.value = '';

        // Automatically hide the error after 5 seconds
        setTimeout(() => {
            elements.errorMessage.style.display = 'none';
        }, 5000);
    }

    /**
     * Handles document type radio button change events
     */
    function handleDocumentTypeChange() {
        // Hide all sub-options first
        document.querySelectorAll('.sub-options').forEach(el => el.style.display = 'none');
        elements.secondPageUpload.style.display = 'none';

        // Show the relevant sub-options based on the selected document type
        switch(this.value) {
            case 'id':
                elements.idTypes.style.display = 'block';
                break;
            case 'tm':
                elements.tmTypes.style.display = 'block';
                break;
            case 'iqama':
                elements.iqamaTypes.style.display = 'block';
                break;
            case 'family-card':
                elements.familyCardTypes.style.display = 'block';
                // Check if 'Two Pages' is already selected
                const twoPageSelected = document.getElementById('two-pages').checked ||
                                       document.getElementById('two-pages-new').checked;
                if (twoPageSelected) {
                    elements.secondPageUpload.style.display = 'block';
                }
                break;
        }
    }

    /**
     * Handles family card type radio button change events
     */
    function handleFamilyCardTypeChange() {
        // Show second page upload area only for two-page options
        const isTwoPages = this.value === 'two-pages' || this.value === 'two-pages-new';
        elements.secondPageUpload.style.display = isTwoPages ? 'block' : 'none';
    }

    /**
     * Collects values from sidebar fields and adds them to the form as hidden inputs
     */
    function collectExternalFields() {
        // Collect all external field values
        const externalFields = {
            optional_prompt: document.getElementById('optional_prompt').value,
        };

        // Create or update hidden inputs for each external field
        Object.entries(externalFields).forEach(([key, value]) => {
            // Check if hidden input already exists
            let hiddenInput = document.querySelector(`input[name="${key}"]`);
            if (!hiddenInput) {
                // Create new hidden input if it doesn't exist
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = key;
                elements.form.appendChild(hiddenInput);
            }
            // Set the current value
            hiddenInput.value = value;
        });
    }

    /**
     * Initializes the translation settings form
     * Sets up event listener for the save settings button
     */
    function initializeTranslationSettings() {
        if (!elements.documentTranslationProvider || !elements.tmTranslationProvider ||
            !elements.openrouterModel || !elements.requestyModel || !elements.saveSettingsBtn) {
            return;
        }

        // Add click event listener to the save settings button
        elements.saveSettingsBtn.addEventListener('click', updateTranslationSettings);
    }

    /**
     * Updates the translation settings via AJAX
     * Sends the new settings to the server and displays a notification
     */
    function updateTranslationSettings() {
        // Get the settings from the form
        const settings = {
            document_translation_provider: elements.documentTranslationProvider.value,
            tm_translation_provider: elements.tmTranslationProvider.value,
            openrouter_model: elements.openrouterModel.value,
            requesty_model: elements.requestyModel.value
        };

        // Show loading state on the button
        const originalButtonText = elements.saveSettingsBtn.innerHTML;
        elements.saveSettingsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        elements.saveSettingsBtn.disabled = true;

        // Send the settings to the server
        fetch('/update_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state
            elements.saveSettingsBtn.innerHTML = originalButtonText;
            elements.saveSettingsBtn.disabled = false;

            // Show notification based on response
            if (data.success) {
                showNotification(data.message, 'success', 'Settings Updated');

                // Update form values if settings were returned
                if (data.settings) {
                    if (data.settings.document_translation_provider) {
                        elements.documentTranslationProvider.value = data.settings.document_translation_provider;
                    }
                    if (data.settings.tm_translation_provider) {
                        elements.tmTranslationProvider.value = data.settings.tm_translation_provider;
                    }
                    if (data.settings.default_openrouter_model) {
                        elements.openrouterModel.value = data.settings.default_openrouter_model;
                    }
                    if (data.settings.default_requesty_model) {
                        elements.requestyModel.value = data.settings.default_requesty_model;
                    }
                }
            } else {
                showNotification(data.message, 'error', 'Error');
            }
        })
        .catch(error => {
            // Reset button state
            elements.saveSettingsBtn.innerHTML = originalButtonText;
            elements.saveSettingsBtn.disabled = false;

            // Show error notification
            showNotification('Failed to update settings: ' + error, 'error', 'Error');
        });
    }

    // Public API
    return {
        init: init
    };
})();

/**
 * Notification System
 * Functions for displaying toast notifications
 */
const NotificationSystem = (function() {
    'use strict';

    const DEFAULT_DURATION = 5000;

    /**
     * Creates and shows a notification
     * @param {string} message - The message to display
     * @param {string} type - The type of notification ('success' or 'error')
     * @param {string} title - The title of the notification
     * @param {number} duration - How long to show the notification in ms
     */
    function showNotification(message, type = 'success', title = '', duration = DEFAULT_DURATION) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        // Set default title if not provided
        if (!title) {
            title = type === 'success' ? 'Success' : 'Error';
        }

        // Set icon based on notification type
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Create notification content
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${iconClass}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
            <div class="notification-progress">
                <div class="notification-progress-bar"></div>
            </div>
        `;

        // Add to container
        container.appendChild(notification);

        // Trigger reflow to enable transition
        notification.offsetHeight;

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Setup close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            closeNotification(notification);
        });

        // Auto close after duration
        setTimeout(() => {
            closeNotification(notification);
        }, duration);
    }

    /**
     * Closes a notification
     * @param {HTMLElement} notification - The notification element to close
     */
    function closeNotification(notification) {
        notification.classList.remove('show');

        // Remove from DOM after transition
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 500);
    }

    // Public API
    return {
        show: showNotification,
        close: closeNotification
    };
})();

// Make showNotification available globally for backward compatibility
window.showNotification = NotificationSystem.show;

/**
 * Copy to Clipboard functionality
 * Handles copying text content to clipboard
 */
const ClipboardSystem = (function() {
    'use strict';

    /**
     * Initialize clipboard functionality
     */
    function init() {
        setupCopyButtons();
    }

    /**
     * Set up event listeners for copy buttons
     */
    function setupCopyButtons() {
        document.querySelectorAll('.copy-btn').forEach(button => {
            button.addEventListener('click', handleCopyClick);
        });
    }

    /**
     * Handle click on copy button
     * @param {Event} e - The click event
     */
    function handleCopyClick(e) {
        const button = e.currentTarget;
        const targetId = button.getAttribute('data-target');
        const targetElement = document.getElementById(targetId);

        if (!targetElement) return;

        // Get the text content to copy
        const textToCopy = targetElement.textContent;

        // Copy to clipboard
        navigator.clipboard.writeText(textToCopy)
            .then(() => {
                // Show success notification
                showNotification('Content copied to clipboard!', 'success', 'Copied', 3000);

                // Visual feedback on button
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';

                // Reset button text after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
                showNotification('Failed to copy to clipboard', 'error', 'Error');
            });
    }

    // Public API
    return {
        init: init
    };
})();

// Initialize the application when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    SwiftSwitch.init();
    ClipboardSystem.init();
});
