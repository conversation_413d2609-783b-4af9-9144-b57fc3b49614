from openai import OpenAI
import P<PERSON>, re, json, os, anthropic, base64, platform, google.generativeai as genai

GEMINI_KEY = os.environ.get('GEMINI_API_KEY')
if not GEMINI_KEY:
    raise ValueError("GEMINI_API_KEY environment variable not set")
OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
REQUESTY_API_KEY = os.environ.get('REQUESTY_API_KEY') or "sk-8caERGwZSpadBma1axz+ymh4ycp7MHm3REDFiNmd3311TRYY0H+nn9FhgQwxF/uU3l/yxuZ0RkRYolUcS7pDFufRpU0l0l1URLrzeD1Huwc="

def call_gemini_v2(model, prompt, image_path=None, admin_prompt=None):
    """
    Calls Google's Gemini model with optional image and admin prompt.
    
    Args:
        model (str): The Gemini model to use (e.g., "gemini-pro", "gemini-pro-vision")
        prompt (str): The main prompt text
        image_path (str, optional): Path to image file
        admin_prompt (str, optional): System prompt/instructions
    """
    print("Selected Provider: Google")
    print(f"Selected Model: {model}")

    genai.configure(api_key=GEMINI_KEY)
    
    # Combine admin_prompt and user prompt if admin_prompt exists
    full_prompt = f"{admin_prompt}\n\n{prompt}" if admin_prompt else prompt
    
    if image_path:
        # Load and process image
        try:
            image = PIL.Image.open(image_path)
            model_instance = genai.GenerativeModel(model_name=model)
            response = model_instance.generate_content([full_prompt, image])
        except Exception as e:
            raise Exception(f"Error processing image or generating content: {str(e)}")
    else:
        # Text-only request
        try:
            model_instance = genai.GenerativeModel(model_name=model)
            response = model_instance.generate_content(full_prompt)
        except Exception as e:
            raise Exception(f"Error generating content: {str(e)}")
        
    ai_response = response.text

    json_ai_response = convert_to_json(ai_response)
    
    return json_ai_response

def call_openrouter(model, prompt, fallback_models=None, image_path=None, admin_prompt=None):
    
    print("Selected Provider: Openrouter")
    print(f"Selected Model: {model}")
    print(f"Fallback Models: {fallback_models}")

    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=OPENROUTER_API_KEY,
    )
    # print("API Key:")
    # print(OPENROUTER_API_KEY)

    
    if image_path:
        # Getting the base64 string
        base64_image = encode_image(image_path)
        
        messages = [
            {
                "role": "system",
                "content": admin_prompt
            } if admin_prompt else None,
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
        
    else:
        messages = [
            {
                "role": "system",
                "content": admin_prompt
            } if admin_prompt else None,
            {
                "role": "user",
                "content": prompt
            }
        ]
    
    # Remove None values from messages
    messages = [msg for msg in messages if msg is not None]
    
    completion = client.chat.completions.create(
        model=model,

        extra_body={
        "models": fallback_models,  # fallback models
        },
        
        messages=messages
    )

    print("Completion:")
    print (completion)
    ai_response = completion.choices[0].message.content
    document_json_string = convert_to_json(ai_response)
    print("after converting to json:")
    print (document_json_string)
    return document_json_string

def call_requesty(model, prompt, fallback_models=None, image_path=None, admin_prompt=None):
    """
    Calls Requesty API with optional image and admin prompt.
    Requesty is OpenAI-compatible and provides advanced routing, cost optimization, and guardrails.

    Args:
        model (str): The model to use (e.g., "anthropic/claude-sonnet-4")
        prompt (str): The main prompt text
        fallback_models (list, optional): List of fallback models
        image_path (str, optional): Path to image file
        admin_prompt (str, optional): System prompt/instructions
    """

    print("Selected Provider: Requesty")
    print(f"Selected Model: {model}")
    print(f"Fallback Models: {fallback_models}")

    client = OpenAI(
        base_url="https://router.requesty.ai/v1",
        api_key=REQUESTY_API_KEY,
    )

    if image_path:
        # Getting the base64 string
        base64_image = encode_image(image_path)

        messages = [
            {
                "role": "system",
                "content": admin_prompt
            } if admin_prompt else None,
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]

    else:
        messages = [
            {
                "role": "system",
                "content": admin_prompt
            } if admin_prompt else None,
            {
                "role": "user",
                "content": prompt
            }
        ]

    # Remove None values from messages
    messages = [msg for msg in messages if msg is not None]

    # Prepare extra_body for Requesty (similar to OpenRouter)
    extra_body = {}
    if fallback_models:
        extra_body["models"] = fallback_models

    completion = client.chat.completions.create(
        model=model,
        extra_body=extra_body,
        messages=messages
    )

    print("Completion:")
    print(completion)
    ai_response = completion.choices[0].message.content
    document_json_string = convert_to_json(ai_response)
    print("after converting to json:")
    print(document_json_string)
    return document_json_string



    # client = OpenAI()

    # # Getting the base64 string
    # base64_image = encode_image(image_path)

    #     example_image_path = os.path.join(TRADEMARKS_TEMPLATES_FOLDER, 'Damin.png')
    #     example_base64_image = encode_image(example_image_path)
    #     example_wrong_output = r"""
    #     {
    #     "main_name": "Damin",
    #     "tm_language": "عربي ولاتيني",
    #     "ar_name": "ض ضامن",
    #     "en_name": "D Damin",
    #     "meaning": "D: حرف لاتيني، وهو الحرف الأول من كلمة ”Damin”\nDamin: كلمة لاتينية تعني: ضامن",
    #     "pronunciation": "دي\nضامن",
    #     "description": "خلفية باللون الأصفر ويوجد في المنتصف حرف D مكتوب بأحرف لاتينية باللون الأسود مع شكل دائري أصفر في الجهتين العلوية والسفلى ويوجد أسفلها كلمة ضامن مكتوبة بأحرف عربية باللون الأسود ويوجد تحتها كلمة دامين مكتوبة بأحرف لاتينية باللون الأسود"
    # }
    #     """

    #     feedback_prompt = r"""
    #     Thank you for your help, but there are some mistakes in your previous response that I would like to point out:
    # 1.	You got the main name and tm language right, well done 
    # 2.	You provided that the ar name is “ض ضامن”, which is not quite right because the letter “ض” does not appear in the logo, the correct ar name is: “ضامن”. Because this is all the arabic words and characters that appear in the logo.
    # 3.	The en name is right, well done. There is the word Damin and the letter D, so the en name becomes “D Damin” (Please put the characters before the words)
    # 4.	For the meaning, you got it good, but please it will be better if you could add more explanation to the word. Also, please put the translation of the letters after the translation of the other words
    # 5.	For the description, it is so unfortunate that you got it mostly wrong, here is what you did wrong:
    # a.	You added English words and characters, which is very bad. Only arabic words and characters must be present in the description, here is how you should write English words and characters: الحرف اللاتيني دي باللون الأسود، كلمة ضامن مكتوبة بأحرف لاتينية باللون الأسود
    # b.	You wrote “دامن” in the description, which is not entirely right, as it should be written exactly as the arabic word “ضامن”, because this is how the word is pronounced in arabic.
    # c.	You did not mention the logo background and shape first, here is how you should begin the description if the logo has a colored background (other than white): بطاقة مربعة باللون الأصفر ويوجد بداخلها....  OR بطاقة مستطيلة باللون الأصفر ويوجد بداخلها...., first mentioned the shape then its color. 
    # d.	Here is the correct way that you could use to write the description: 
    # بطاقة مربعة باللون الأصفر ويوجد بداخلها الحرف اللاتيني دي حيث يوجد حوله ثلاث أشكال دائرية باللون الأسود ويوجد أسفله كلمة ضامن مكتوبة بأحرف عربية باللون الأسود ويوجد أسفلها كلمة ضامن مكتوبة بأحرف لاتينية باللون الأسود
    # 6.	For the pronunciation, you got it right, good. But please mention the letters first, then the words, like this: "دي\nضامن".
    # Taking these notes into account, please go ahead and translate this new logo:

    #     """

    #     response = client.chat.completions.create(
    #     model="gpt-4o",
    #     messages=[
    #         {"role": "system", "content": "You are a professional trademark analyst, you are accurate and you follow commands well"},
    #         {
    #         "role": "user",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": prompt,
    #             },
    #             {
    #             "type": "image_url",
    #             "image_url": {
    #                 "url":  f"data:image/jpeg;base64,{example_base64_image}"
    #             },
    #             },
    #         ],
    #         },
    #         {
    #         "role": "assistant",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": example_wrong_output,
    #             },
    #         ],
    #         },
    #         {
    #         "role": "user",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": feedback_prompt,
    #             },
    #             {
    #             "type": "image_url",
    #             "image_url": {
    #                 "url":  f"data:image/jpeg;base64,{base64_image}"
    #             },
    #             },
    #         ],
    #         },
    #     ],
    #     )
    
    # response = client.chat.completions.create(
    # model="gpt-4o",
    # messages=[
    #     {"role": "system", "content": "You are a professional trademark analyst, you are accurate and you follow commands well"},
    #     {
    #     "role": "user",
    #     "content": [
    #         {
    #         "type": "text",
    #         "text": prompt,
    #         },
    #         {
    #         "type": "image_url",
    #         "image_url": {
    #             "url":  f"data:image/jpeg;base64,{base64_image}"
    #         },
    #         },
    #     ],
    #     }
    # ]
    # )
    # response = response.choices[0].message.content
    # print(response)
    # return response

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        # print("image64")
        # print(base64.b64encode(image_file.read()).decode('utf-8'))
        return base64.b64encode(image_file.read()).decode('utf-8')

def convert_to_json(ai_response):
    """
    Convert a string containing JSON data to a Python dictionary.
    Handles Arabic text and special characters properly.
    
    Args:
        ai_response (str): String containing JSON data
        
    Returns:
        dict: Parsed JSON data or None if parsing fails
    """
    print("received string:")
    print (ai_response)
    # Remove any "json" prefix and clean whitespace
    cleaned_response = ai_response.strip()
    if cleaned_response.startswith('json'):
        cleaned_response = cleaned_response[4:].strip()
    
    try:
        # Try direct JSON parsing first
        return json.loads(cleaned_response, strict=False)
    except json.JSONDecodeError as e:
        print(f"Initial JSON parsing failed: {e}")
        
        try:
            # Try to extract JSON content between outermost curly braces
            start_idx = cleaned_response.find('{')
            end_idx = cleaned_response.rindex('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = cleaned_response[start_idx:end_idx]
                return json.loads(json_str, strict=False)
        except Exception as e:
            print(f"JSON extraction failed: {e}")
            
        try:
            # Final attempt: clean and normalize the string
            normalized = cleaned_response.encode('utf-8').decode('utf-8')
            return json.loads(normalized, strict=False)
        except Exception as e:
            print(f"Final parsing attempt failed: {e}")
    
    return None

