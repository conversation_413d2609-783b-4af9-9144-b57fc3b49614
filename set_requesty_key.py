#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set the Requesty API key as an environment variable.
Run this script before starting the Flask app to set the API key.
"""

import os

# Set the Requesty API key
REQUESTY_API_KEY = "sk-8caERGwZSpadBma1axz+ymh4ycp7MHm3REDFiNmd3311TRYY0H+nn9FhgQwxF/uU3l/yxuZ0RkRYolUcS7pDFufRpU0l0l1URLrzeD1Huwc="

# Set the environment variable
os.environ['REQUESTY_API_KEY'] = REQUESTY_API_KEY

print("Requesty API key has been set as environment variable.")
print("You can now start your Flask app.")

# Optionally, you can also add this to a .env file for persistence
try:
    with open('.env', 'a') as f:
        f.write(f'\nREQUESTY_API_KEY={REQUESTY_API_KEY}\n')
    print("API key also added to .env file.")
except Exception as e:
    print(f"Could not write to .env file: {e}")
